# Database Configuration
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Session Configuration
SESSION_SECRET=your-secret-key-change-in-production

# Image Storage Configuration
# Choose ONE storage option:

# Option 1: Local storage (simple, no external dependencies)
# Images stored on server - good for development

# Option 2: Cloudinary (25GB free, global CDN, recommended for production)
# Get these values from Cloudinary Console > Dashboard > Account Details
# CLOUDINARY_CLOUD_NAME=your-cloud-name
# CLOUDINARY_API_KEY=***************
# CLOUDINARY_API_SECRET=abcdefghijklmnopqrstuvwxyz123456

# Server Configuration
PORT=5000
NODE_ENV=development

// Image optimization utilities for optimal Cloudinary performance and storage efficiency

/**
 * Compress an image file to reduce size while maintaining quality
 */
export function compressImage(
  file: File,
  maxWidth: number = 800,
  maxHeight: number = 600,
  quality: number = 0.8
): Promise<File> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;
      
      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      // Set canvas dimensions
      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          } else {
            reject(new Error('Failed to compress image'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
}

/**
 * Get optimized file size info
 */
export function getFileSizeInfo(file: File) {
  const sizeInMB = file.size / (1024 * 1024);
  const isOptimal = sizeInMB <= 0.5; // 500KB or less is optimal
  
  return {
    sizeInMB: Math.round(sizeInMB * 100) / 100,
    sizeInKB: Math.round(file.size / 1024),
    isOptimal,
    recommendation: isOptimal
      ? 'Optimal size for fast loading'
      : 'Consider compressing for better performance'
  };
}

/**
 * Batch compress multiple images
 */
export async function compressImages(
  files: File[],
  options: {
    maxWidth?: number;
    maxHeight?: number;
    quality?: number;
  } = {}
): Promise<File[]> {
  const {
    maxWidth = 800,
    maxHeight = 600,
    quality = 0.8
  } = options;

  const compressionPromises = files.map(file => {
    const sizeInfo = getFileSizeInfo(file);
    
    // Only compress if file is larger than 500KB
    if (sizeInfo.sizeInMB > 0.5) {
      return compressImage(file, maxWidth, maxHeight, quality);
    }
    
    return Promise.resolve(file);
  });

  return Promise.all(compressionPromises);
}

/**
 * Calculate storage usage for Cloudinary monitoring
 */
export function calculateStorageUsage(imageCount: number, avgSizeKB: number = 300) {
  const totalSizeGB = (imageCount * avgSizeKB) / (1024 * 1024);
  const freeLimit = 25; // 25GB Cloudinary free tier limit
  const usagePercentage = (totalSizeGB / freeLimit) * 100;

  return {
    totalSizeGB: Math.round(totalSizeGB * 100) / 100,
    freeLimit,
    usagePercentage: Math.round(usagePercentage),
    remainingGB: Math.round((freeLimit - totalSizeGB) * 100) / 100,
    isWithinFreeLimit: totalSizeGB <= freeLimit
  };
}

/**
 * Get recommendations for staying within free tier
 */
export function getFreeTierRecommendations(imageCount: number, avgSizeKB: number) {
  const usage = calculateStorageUsage(imageCount, avgSizeKB);
  const recommendations: string[] = [];
  
  if (usage.usagePercentage > 80) {
    recommendations.push('⚠️ Approaching free tier limit');
    recommendations.push('Consider compressing existing images');
  }
  
  if (avgSizeKB > 500) {
    recommendations.push('💡 Compress images to ~200-300KB each');
    recommendations.push('This could reduce storage by 40-60%');
  }
  
  if (usage.usagePercentage < 50) {
    recommendations.push('✅ Well within free tier limits');
    recommendations.push(`You can add ${Math.floor(usage.remainingGB * 1024 * 1024 / avgSizeKB)} more images`);
  }
  
  return recommendations;
}

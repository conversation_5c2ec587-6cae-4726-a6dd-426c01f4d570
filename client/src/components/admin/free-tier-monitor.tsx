import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Cloud, HardDrive, Zap, AlertTriangle, CheckCircle } from "lucide-react";
import { calculateStorageUsage, getFreeTierRecommendations } from "@/lib/image-utils";

interface FreeTierMonitorProps {
  totalProducts: number;
  totalImages: number;
  averageImageSizeKB?: number;
}

export function FreeTierMonitor({ 
  totalProducts, 
  totalImages, 
  averageImageSizeKB = 300 
}: FreeTierMonitorProps) {
  const usage = calculateStorageUsage(totalImages, averageImageSizeKB);
  const recommendations = getFreeTierRecommendations(totalImages, averageImageSizeKB);

  const getStatusColor = (percentage: number) => {
    if (percentage < 50) return "text-green-600";
    if (percentage < 80) return "text-yellow-600";
    return "text-red-600";
  };

  const getStatusIcon = (percentage: number) => {
    if (percentage < 50) return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (percentage < 80) return <Zap className="h-4 w-4 text-yellow-600" />;
    return <AlertTriangle className="h-4 w-4 text-red-600" />;
  };

  const getStatusBadge = (percentage: number) => {
    if (percentage < 50) return <Badge variant="secondary" className="bg-green-100 text-green-800">Excellent</Badge>;
    if (percentage < 80) return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Good</Badge>;
    return <Badge variant="destructive">Approaching Limit</Badge>;
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Cloud className="h-4 w-4" />
          Google Cloud Free Tier Usage
        </CardTitle>
        {getStatusBadge(usage.usagePercentage)}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Storage Usage */}
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="flex items-center gap-2">
                <HardDrive className="h-3 w-3" />
                Storage Used
              </span>
              <span className={getStatusColor(usage.usagePercentage)}>
                {usage.totalSizeGB} GB / {usage.freeLimit} GB
              </span>
            </div>
            <Progress 
              value={usage.usagePercentage} 
              className="h-2"
            />
            <div className="flex items-center justify-between text-xs text-gray-500">
              <span>{usage.usagePercentage}% used</span>
              <span>{usage.remainingGB} GB remaining</span>
            </div>
          </div>

          {/* Statistics */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-1">
              <div className="text-gray-500">Total Products</div>
              <div className="font-medium">{totalProducts.toLocaleString()}</div>
            </div>
            <div className="space-y-1">
              <div className="text-gray-500">Total Images</div>
              <div className="font-medium">{totalImages.toLocaleString()}</div>
            </div>
            <div className="space-y-1">
              <div className="text-gray-500">Avg Image Size</div>
              <div className="font-medium">{averageImageSizeKB} KB</div>
            </div>
            <div className="space-y-1">
              <div className="text-gray-500">Status</div>
              <div className="flex items-center gap-1">
                {getStatusIcon(usage.usagePercentage)}
                <span className={`text-xs ${getStatusColor(usage.usagePercentage)}`}>
                  {usage.isWithinFreeLimit ? 'Free Tier' : 'Paid Usage'}
                </span>
              </div>
            </div>
          </div>

          {/* Recommendations */}
          {recommendations.length > 0 && (
            <div className="space-y-2">
              <div className="text-sm font-medium text-gray-700">Recommendations:</div>
              <div className="space-y-1">
                {recommendations.map((rec, index) => (
                  <div key={index} className="text-xs text-gray-600 flex items-start gap-1">
                    <span className="mt-0.5">•</span>
                    <span>{rec}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Free Tier Benefits */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="text-sm font-medium text-green-800 mb-2">
              🆓 Free Tier Benefits Active
            </div>
            <div className="text-xs text-green-700 space-y-1">
              <div>• 5 GB storage per month</div>
              <div>• 1 GB bandwidth per month</div>
              <div>• 5,000 uploads per month</div>
              <div>• 50,000 downloads per month</div>
              <div>• No time limit - free forever!</div>
            </div>
          </div>

          {/* Upgrade Notice */}
          {usage.usagePercentage > 90 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <div className="text-sm font-medium text-yellow-800 mb-1">
                ⚠️ Approaching Free Tier Limit
              </div>
              <div className="text-xs text-yellow-700">
                Consider compressing existing images or upgrading to paid tier for unlimited storage.
                Cloudinary overage costs are very low (~$0.02 USD/GB/month).
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

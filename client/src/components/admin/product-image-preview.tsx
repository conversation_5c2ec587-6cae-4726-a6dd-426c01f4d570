import { useState } from "react";
import { ChevronLeft, ChevronRight, Images } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ProductImagePreviewProps {
  images: string[];
  productName: string;
  className?: string;
}

export function ProductImagePreview({ 
  images, 
  productName, 
  className = "" 
}: ProductImagePreviewProps) {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Prepare images array (support both imageUrl and imageUrls)
  const imageList = images && images.length > 0 ? images : [];
  const hasMultipleImages = imageList.length > 1;

  if (imageList.length === 0) {
    return (
      <div className={`w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center ${className}`}>
        <Images className="h-4 w-4 text-gray-400" />
      </div>
    );
  }

  const currentImage = imageList[currentIndex];

  const goToPrevious = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex((prev) => 
      prev === 0 ? imageList.length - 1 : prev - 1
    );
  };

  const goToNext = (e: React.MouseEvent) => {
    e.stopPropagation();
    setCurrentIndex((prev) => 
      prev === imageList.length - 1 ? 0 : prev + 1
    );
  };

  return (
    <div className={`relative group ${className}`}>
      {/* Main Image */}
      <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
        <img
          src={currentImage}
          alt={`${productName} - Image ${currentIndex + 1}`}
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = '/placeholder-image.jpg';
          }}
        />

        {/* Navigation Arrows (only show if multiple images) */}
        {hasMultipleImages && (
          <>
            <Button
              variant="ghost"
              size="icon"
              className="absolute left-0 top-1/2 -translate-y-1/2 h-6 w-6 bg-black/60 hover:bg-black/80 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              onClick={goToPrevious}
            >
              <ChevronLeft className="h-3 w-3" />
            </Button>
            
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-0 top-1/2 -translate-y-1/2 h-6 w-6 bg-black/60 hover:bg-black/80 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              onClick={goToNext}
            >
              <ChevronRight className="h-3 w-3" />
            </Button>
          </>
        )}

        {/* Image Counter */}
        {hasMultipleImages && (
          <div className="absolute top-1 right-1 bg-black/70 text-white text-xs px-1 rounded">
            {currentIndex + 1}/{imageList.length}
          </div>
        )}

        {/* Primary Image Badge */}
        {currentIndex === 0 && hasMultipleImages && (
          <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent">
            <div className="text-white text-xs px-1 py-0.5 text-center">
              Primary
            </div>
          </div>
        )}
      </div>

      {/* Multiple Images Indicator Dots */}
      {hasMultipleImages && (
        <div className="absolute -bottom-2 left-1/2 -translate-x-1/2 flex gap-1">
          {imageList.map((_, index) => (
            <div
              key={index}
              className={`w-1.5 h-1.5 rounded-full transition-all duration-200 ${
                index === currentIndex
                  ? 'bg-forest'
                  : 'bg-gray-300'
              }`}
            />
          ))}
        </div>
      )}
    </div>
  );
}

// Expanded version for hover/modal view
export function ProductImagePreviewExpanded({ 
  images, 
  productName, 
  className = "" 
}: ProductImagePreviewProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const imageList = images && images.length > 0 ? images : [];
  const hasMultipleImages = imageList.length > 1;

  if (imageList.length === 0) {
    return (
      <div className={`aspect-square bg-gray-100 rounded-lg flex items-center justify-center ${className}`}>
        <Images className="h-8 w-8 text-gray-400" />
      </div>
    );
  }

  const currentImage = imageList[currentIndex];

  const goToPrevious = () => {
    setCurrentIndex((prev) => 
      prev === 0 ? imageList.length - 1 : prev - 1
    );
  };

  const goToNext = () => {
    setCurrentIndex((prev) => 
      prev === imageList.length - 1 ? 0 : prev + 1
    );
  };

  return (
    <div className={`relative ${className}`}>
      {/* Main Image Display */}
      <div className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden group">
        <img
          src={currentImage}
          alt={`${productName} - Image ${currentIndex + 1}`}
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = '/placeholder-image.jpg';
          }}
        />

        {/* Navigation Arrows */}
        {hasMultipleImages && (
          <>
            <Button
              variant="outline"
              size="icon"
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white h-8 w-8"
              onClick={goToPrevious}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <Button
              variant="outline"
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white h-8 w-8"
              onClick={goToNext}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </>
        )}

        {/* Image Counter */}
        {hasMultipleImages && (
          <div className="absolute top-2 right-2 bg-black/70 text-white text-sm px-2 py-1 rounded">
            {currentIndex + 1} / {imageList.length}
          </div>
        )}

        {/* Primary Image Badge */}
        {currentIndex === 0 && (
          <div className="absolute top-2 left-2 bg-forest text-white text-sm px-2 py-1 rounded">
            Primary
          </div>
        )}
      </div>

      {/* Thumbnail Navigation */}
      {hasMultipleImages && (
        <div className="flex gap-2 mt-3 justify-center">
          {imageList.map((image, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-12 h-12 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                index === currentIndex
                  ? 'border-forest shadow-md'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <img
                src={image}
                alt={`${productName} thumbnail ${index + 1}`}
                className="w-full h-full object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/placeholder-image.jpg';
                }}
              />
            </button>
          ))}
        </div>
      )}
    </div>
  );
}

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ShoppingCart, Star, MessageSquare } from "lucide-react";
import { useCart } from "./cart-context";
import { ProductVariationModal } from "./product-variation-modal";
import { useToast } from "@/hooks/use-toast";
import { useQuery } from "@tanstack/react-query";

import type { Product, ProductVariation } from "@shared/schema";
import { useLocation } from "wouter";

interface ProductCardProps {
  product: Product;
}

export function ProductCard({ product }: ProductCardProps) {
  const [isAdding, setIsAdding] = useState(false);
  const [showVariationModal, setShowVariationModal] = useState(false);
  const [modalType, setModalType] = useState<'cart' | 'enquiry'>('cart');
  const [selectedVariation, setSelectedVariation] = useState<ProductVariation | null>(null);
  const { addToCart, items } = useCart();
  const { toast } = useToast();
  const [, setLocation] = useLocation();

  // Fetch variations if product has variations
  const { data: variations = [] } = useQuery<ProductVariation[]>({
    queryKey: [`/api/products/${product.id}/variations`],
    enabled: product.hasVariations,
  });

  // Set first variation as default when variations are loaded
  useEffect(() => {
    if (product.hasVariations && variations.length > 0 && !selectedVariation) {
      const firstActiveVariation = variations.find(v => v.isActive) || variations[0];
      setSelectedVariation(firstActiveVariation);
    }
  }, [variations, product.hasVariations, selectedVariation]);

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.stopPropagation();

    // If product has variations but no variation is selected, show modal
    if (product.hasVariations && !selectedVariation) {
      setModalType('cart');
      setShowVariationModal(true);
      return;
    }

    setIsAdding(true);
    try {
      await addToCart(product.id, 1, selectedVariation?.id);
      toast({
        title: "Added to cart",
        description: `${product.name} has been added to your enquiry cart.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add item to enquiry cart. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsAdding(false);
    }
  };

  const handleSubmitEnquiry = (e: React.MouseEvent) => {
    e.stopPropagation();

    // If product has variations but no variation is selected, show modal
    if (product.hasVariations && !selectedVariation) {
      setModalType('enquiry');
      setShowVariationModal(true);
      return;
    }

    // Get current price and carton info (from variation or product)
    const currentPrice = selectedVariation?.price || product.price;
    const currentUnitsPerCarton = selectedVariation?.unitsPerCarton || (product as any).unitsPerCarton || 1;
    const currentUnitType = selectedVariation?.unitType || (product as any).unitType || 'pieces';

    // Create enquiry data
    const enquiryData = {
      productId: product.id,
      productName: product.name,
      productDescription: product.description,
      productImageUrl: product.imageUrl,
      quantity: 1,
      price: currentPrice,
      // Variation details
      variationId: selectedVariation?.id,
      variationName: selectedVariation?.name,
      variationType: selectedVariation?.type,
      variationValue: selectedVariation?.value,
      // Carton information
      unitsPerCarton: currentUnitsPerCarton,
      unitType: currentUnitType,
      pricePerUnit: (parseFloat(currentPrice) / currentUnitsPerCarton).toFixed(2),
    };

    // Store enquiry data in sessionStorage and navigate to checkout page
    sessionStorage.setItem('directEnquiryData', JSON.stringify([enquiryData]));
    setLocation('/checkout');
  };

  const handleCardClick = () => {
    setLocation(`/products/${product.id}`);
  };

  // Helper functions to get current values based on selected variation
  const getCurrentPrice = () => {
    return selectedVariation?.price || product.price;
  };

  const getCurrentOriginalPrice = () => {
    return selectedVariation?.originalPrice || product.originalPrice;
  };

  const getCurrentUnitsPerCarton = () => {
    return selectedVariation?.unitsPerCarton || (product as any).unitsPerCarton || 1;
  };

  const getCurrentUnitType = () => {
    return selectedVariation?.unitType || (product as any).unitType || 'pieces';
  };

  const getCurrentCartonWeight = () => {
    return selectedVariation?.cartonWeight || (product as any).cartonWeight;
  };

  const getCurrentCartonDimensions = () => {
    return selectedVariation?.cartonDimensions || (product as any).cartonDimensions;
  };

  const currentPrice = getCurrentPrice();
  const currentOriginalPrice = getCurrentOriginalPrice();
  const hasDiscount = currentOriginalPrice && parseFloat(currentOriginalPrice) > parseFloat(currentPrice);
  const discountPercentage = hasDiscount
    ? Math.round(((parseFloat(currentOriginalPrice!) - parseFloat(currentPrice)) / parseFloat(currentOriginalPrice!)) * 100)
    : 0;

  // Use only the primary image for product listing
  const primaryImage = product.imageUrl || '/placeholder.svg';

  // Check if product is already in cart (considering variations)
  const isInCart = items.some(item =>
    item.productId === product.id &&
    (product.hasVariations ? item.variationId === selectedVariation?.id : true)
  );

  return (
    <>
      <Card
        className="group cursor-pointer hover:shadow-xl transition-all duration-300 overflow-hidden"
        onClick={handleCardClick}
      >
        <div className="relative aspect-[4/3] overflow-hidden">
          <img
            src={primaryImage}
            alt={product.name}
            className="w-full h-full object-cover transition-all duration-300 hover:scale-105 cursor-pointer"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = '/placeholder.svg';
            }}
          />
          <div className="absolute top-3 left-3">
            <Badge className="bg-sage text-white">
              <span className="mr-1">🌿</span>
              Eco-Friendly
            </Badge>
          </div>
          {hasDiscount && (
            <div className="absolute top-3 right-3">
              <Badge className="bg-terracotta text-white">
                -{discountPercentage}%
              </Badge>
            </div>
          )}
          {product.featured && (
            <div className="absolute top-12 right-3">
              <Badge className="bg-yellow-500 text-white">
                Featured
              </Badge>
            </div>
          )}
        </div>

        <CardContent className="p-4">
          <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{product.name}</h3>
          <p className="text-sm text-muted-foreground mb-3 line-clamp-2">{product.description}</p>

          {/* Variation Selection */}
          {product.hasVariations && variations.length > 0 && (
            <div className="mb-3">
              <Select
                value={selectedVariation?.id.toString() || ""}
                onValueChange={(value) => {
                  const variation = variations.find(v => v.id.toString() === value);
                  setSelectedVariation(variation || null);
                }}
              >
                <SelectTrigger className="w-full h-8 text-xs">
                  <SelectValue placeholder="Select variation..." />
                </SelectTrigger>
                <SelectContent>
                  {variations
                    .filter(variation => variation.isActive)
                    .map((variation) => (
                      <SelectItem key={variation.id} value={variation.id.toString()}>
                        <div className="flex items-center justify-between w-full">
                          <span className="text-xs">{variation.name} - {variation.value}</span>
                          {variation.price && (
                            <span className="text-xs font-medium text-forest ml-2">
                              ₹{parseFloat(variation.price).toFixed(2)}
                            </span>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
              {selectedVariation && (
                <div className="text-xs text-muted-foreground mt-1">
                  Selected: {selectedVariation.name} • {selectedVariation.value}
                </div>
              )}
            </div>
          )}

          <div className="space-y-2 mb-3">
            {/* Price Information */}
            <div className="flex justify-between items-start">
              <div className="flex flex-col">
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-bold text-forest">
                    ₹{parseFloat(currentPrice).toFixed(2)}
                  </span>
                  {hasDiscount && (
                    <span className="text-sm text-muted-foreground line-through">
                      ₹{parseFloat(currentOriginalPrice!).toFixed(2)}
                    </span>
                  )}
                </div>
                {/* Carton Information */}
                <div className="text-xs text-muted-foreground">
                  per carton ({getCurrentUnitsPerCarton()} {getCurrentUnitType()})
                </div>
                {/* Price per unit */}
                <div className="text-xs text-forest font-medium">
                  ₹{(parseFloat(currentPrice) / getCurrentUnitsPerCarton()).toFixed(2)} per {getCurrentUnitType().slice(0, -1)}
                </div>
              </div>
              <div className="flex items-center space-x-1">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="text-sm text-muted-foreground">
                  {parseFloat(product.rating).toFixed(1)} ({product.reviewCount})
                </span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            <Button
              className={`w-full text-white ${
                isInCart
                  ? "bg-gray-400 cursor-not-allowed"
                  : "bg-forest hover:bg-forest/90"
              }`}
              onClick={handleAddToCart}
              disabled={isAdding || !product.inStock || isInCart}
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              {isAdding
                ? "Adding..."
                : !product.inStock
                  ? "Out of Stock"
                  : isInCart
                    ? "Added to Cart"
                    : "Add to Cart"
              }
            </Button>

            <Button
              variant="outline"
              className="w-full border-sage text-sage hover:bg-sage hover:text-white"
              onClick={handleSubmitEnquiry}
              disabled={!product.inStock}
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              Submit Enquiry
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Variation Selection Modal - Outside Card to prevent event bubbling */}
      {showVariationModal && (
        <ProductVariationModal
          product={product}
          isOpen={showVariationModal}
          onClose={() => setShowVariationModal(false)}
          mode={modalType}
          onAddToCart={() => {
            // Optional: Additional callback after successful add to cart
          }}
          onSubmitEnquiry={(enquiryData) => {
            // Store enquiry data and navigate to checkout page
            sessionStorage.setItem('directEnquiryData', JSON.stringify([enquiryData]));
            setLocation('/checkout');
          }}
        />
      )}
    </>
  );
}

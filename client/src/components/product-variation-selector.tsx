import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Plus, Minus, Package, Ruler, Palette, Star, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import type { ProductVariation } from "@shared/schema";

interface ProductVariationSelectorProps {
  productId: number;
  onVariationChange: (variation: ProductVariation | null, quantity: number) => void;
  className?: string;
}

export function ProductVariationSelector({ 
  productId, 
  onVariationChange, 
  className 
}: ProductVariationSelectorProps) {
  const [selectedVariation, setSelectedVariation] = useState<ProductVariation | null>(null);
  const [quantity, setQuantity] = useState(1);

  const { data: variations = [], isLoading } = useQuery<ProductVariation[]>({
    queryKey: [`/api/products/${productId}/variations`],
    enabled: !!productId,
  });

  // Group variations by type
  const variationsByType = variations.reduce((acc, variation) => {
    if (!acc[variation.type]) {
      acc[variation.type] = [];
    }
    acc[variation.type].push(variation);
    return acc;
  }, {} as Record<string, ProductVariation[]>);

  // Sort variations within each type by sortOrder
  Object.keys(variationsByType).forEach(type => {
    variationsByType[type].sort((a, b) => a.sortOrder - b.sortOrder);
  });

  const handleVariationSelect = (variationId: string) => {
    const variation = variations.find(v => v.id.toString() === variationId);
    setSelectedVariation(variation || null);
    setQuantity(1); // Reset quantity when variation changes
  };

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && (!selectedVariation || newQuantity <= selectedVariation.stockQuantity)) {
      setQuantity(newQuantity);
    }
  };

  // Notify parent component when variation or quantity changes
  useEffect(() => {
    onVariationChange(selectedVariation, quantity);
  }, [selectedVariation, quantity, onVariationChange]);

  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'size':
        return <Ruler className="h-4 w-4" />;
      case 'color':
        return <Palette className="h-4 w-4" />;
      case 'model':
        return <Package className="h-4 w-4" />;
      default:
        return <Star className="h-4 w-4" />;
    }
  };

  const getTypeLabel = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  if (isLoading) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (variations.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Variation Selectors */}
      {Object.entries(variationsByType).map(([type, typeVariations]) => (
        <div key={type} className="space-y-3">
          <label className="flex items-center gap-2 text-sm font-medium text-gray-900">
            {getTypeIcon(type)}
            Select {getTypeLabel(type)}
            <span className="text-red-500">*</span>
          </label>
          
          <Select onValueChange={handleVariationSelect}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder={`Choose ${type}...`} />
            </SelectTrigger>
            <SelectContent>
              {typeVariations
                .filter(variation => variation.isActive)
                .map((variation) => (
                  <SelectItem 
                    key={variation.id} 
                    value={variation.id.toString()}
                    disabled={variation.stockQuantity === 0}
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{variation.name}</span>
                        <span className="text-sm text-gray-500">({variation.value})</span>
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        {variation.price && (
                          <span className="text-sm font-medium text-forest">
                            ₹{parseFloat(variation.price).toFixed(2)}
                          </span>
                        )}
                        <Badge 
                          variant={variation.stockQuantity > 0 ? "secondary" : "destructive"}
                          className="text-xs"
                        >
                          {variation.stockQuantity > 0 ? `${variation.stockQuantity} left` : "Out of stock"}
                        </Badge>
                      </div>
                    </div>
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>
      ))}

      {/* Selected Variation Details */}
      {selectedVariation && (
        <Card className="border-forest/20 bg-forest/5">
          <CardContent className="p-4">
            <div className="space-y-4">
              {/* Variation Info */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getTypeIcon(selectedVariation.type)}
                  <span className="font-medium text-gray-900">
                    {selectedVariation.name} ({selectedVariation.value})
                  </span>
                </div>
                <div className="text-right">
                  {selectedVariation.price ? (
                    <div className="flex items-center gap-2">
                      <span className="text-lg font-bold text-forest">
                        ₹{parseFloat(selectedVariation.price).toFixed(2)}
                      </span>
                      {selectedVariation.originalPrice && (
                        <span className="text-sm text-gray-500 line-through">
                          ₹{parseFloat(selectedVariation.originalPrice).toFixed(2)}
                        </span>
                      )}
                    </div>
                  ) : (
                    <span className="text-sm text-gray-600">Base price applies</span>
                  )}
                </div>
              </div>

              {/* Stock Status */}
              <div className="flex items-center gap-2">
                <Badge 
                  variant={selectedVariation.stockQuantity > 10 ? "secondary" : "destructive"}
                  className="text-xs"
                >
                  {selectedVariation.stockQuantity > 0 
                    ? `${selectedVariation.stockQuantity} units available` 
                    : "Out of stock"
                  }
                </Badge>
                {selectedVariation.stockQuantity <= 5 && selectedVariation.stockQuantity > 0 && (
                  <div className="flex items-center gap-1 text-amber-600">
                    <AlertCircle className="h-3 w-3" />
                    <span className="text-xs">Low stock</span>
                  </div>
                )}
              </div>

              {/* Quantity Selector - Always show when variation is selected */}
              <div className="space-y-3">
                <label className="text-sm font-medium text-gray-900">Quantity</label>
                <div className="flex items-center gap-3">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handleQuantityChange(quantity - 1)}
                    disabled={quantity <= 1}
                    className="h-10 w-10 rounded-full"
                  >
                    <Minus className="h-4 w-4" />
                  </Button>
                  
                  <div className="flex-1 text-center">
                    <div className="text-lg font-semibold bg-gray-50 rounded-lg py-2 px-4 min-w-[80px]">
                      {quantity}
                    </div>
                  </div>
                  
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handleQuantityChange(quantity + 1)}
                    disabled={quantity >= selectedVariation.stockQuantity}
                    className="h-10 w-10 rounded-full"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                
                {quantity >= selectedVariation.stockQuantity && (
                  <p className="text-sm text-amber-600 text-center">
                    Maximum available quantity: {selectedVariation.stockQuantity}
                  </p>
                )}
              </div>

              {/* Total Price */}
              {selectedVariation.price && (
                <div className="border-t pt-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-700">
                      Total ({quantity} × ₹{parseFloat(selectedVariation.price).toFixed(2)})
                    </span>
                    <span className="text-lg font-bold text-forest">
                      ₹{(parseFloat(selectedVariation.price) * quantity).toFixed(2)}
                    </span>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Help Text */}
      {!selectedVariation && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
          <div className="flex items-start gap-2">
            <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-amber-800">Selection Required</p>
              <p className="text-xs text-amber-700 mt-1">
                Please select all product options above before adding to cart or submitting an enquiry.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

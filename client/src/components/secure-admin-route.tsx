import { ReactNode, useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { AdminLogin } from "./admin-login";
import { TwoFactorSetup } from "./two-factor-setup";
import { Card, CardContent } from "@/components/ui/card";
import { Loader2, Shield } from "lucide-react";

interface SecureAdminRouteProps {
  children: ReactNode;
}

interface AuthStatus {
  authenticated: boolean;
  isAdmin: boolean;
  twoFactorVerified: boolean;
  pendingTwoFactor: boolean;
  csrfToken: string;
}

export function SecureAdminRoute({ children }: SecureAdminRouteProps) {
  const [user, setUser] = useState<any>(null);
  const [showTwoFactorSetup, setShowTwoFactorSetup] = useState(false);

  // Check authentication status
  const { data: authStatus, isLoading, refetch } = useQuery<AuthStatus>({
    queryKey: ['/api/auth/status'],
    queryFn: async () => {
      const response = await fetch('/api/auth/status', {
        credentials: 'include',
      });
      if (response.ok) {
        return await response.json();
      }
      return { 
        authenticated: false, 
        isAdmin: false, 
        twoFactorVerified: false, 
        pendingTwoFactor: false,
        csrfToken: '' 
      };
    },
    refetchInterval: 30000, // Refresh every 30 seconds
    retry: false,
  });

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-forest/10 to-earth/10">
        <Card className="w-96">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-forest mb-4" />
            <p className="text-gray-600">Checking authentication...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Not authenticated - show login
  if (!authStatus?.authenticated || !authStatus?.isAdmin) {
    return (
      <AdminLogin 
        onLoginSuccess={(userData) => {
          setUser(userData);
          refetch(); // Refresh auth status
        }} 
      />
    );
  }

  // Pending 2FA verification
  if (authStatus.pendingTwoFactor && !authStatus.twoFactorVerified) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-forest/10 to-earth/10">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Shield className="h-12 w-12 text-forest mb-4" />
            <h2 className="text-xl font-bold text-forest mb-2">Two-Factor Authentication Required</h2>
            <p className="text-gray-600 text-center mb-4">
              Please complete two-factor authentication to access the admin panel.
            </p>
            <p className="text-sm text-gray-500 text-center">
              Use your authenticator app to generate a verification code.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show 2FA setup if requested
  if (showTwoFactorSetup) {
    return (
      <TwoFactorSetup
        onSetupComplete={() => {
          setShowTwoFactorSetup(false);
          refetch(); // Refresh auth status
        }}
        onCancel={() => setShowTwoFactorSetup(false)}
      />
    );
  }

  // Authenticated and verified - show admin content
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Security status bar */}
      <div className="bg-green-600 text-white px-4 py-2 text-sm">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Shield className="h-4 w-4" />
            <span>Secure Admin Session Active</span>
          </div>
          <div className="flex items-center space-x-4">
            {!authStatus.twoFactorVerified && (
              <button
                onClick={() => setShowTwoFactorSetup(true)}
                className="text-green-100 hover:text-white underline text-xs"
              >
                Setup 2FA for Enhanced Security
              </button>
            )}
            <span className="text-green-100 text-xs">
              Session expires in 4 hours
            </span>
          </div>
        </div>
      </div>
      
      {/* Admin content */}
      {children}
    </div>
  );
}

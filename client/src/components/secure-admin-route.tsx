import { ReactNode, useState } from "react";
import { AdminLogin } from "./admin-login";
import { TwoFactorSetup } from "./two-factor-setup";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "./auth-context";
import { Loader2, Shield, Smartphone } from "lucide-react";

interface SecureAdminRouteProps {
  children: ReactNode;
}

interface AuthStatus {
  authenticated: boolean;
  isAdmin: boolean;
  twoFactorVerified: boolean;
  pendingTwoFactor: boolean;
  csrfToken: string;
}

export function SecureAdminRoute({ children }: SecureAdminRouteProps) {
  const [showTwoFactorSetup, setShowTwoFactorSetup] = useState(false);
  const [twoFactorCode, setTwoFactorCode] = useState("");
  const [twoFactorLoading, setTwoFactorLoading] = useState(false);
  const [error, setError] = useState("");
  const { toast } = useToast();

  // Use global auth context
  const {
    isAuthenticated,
    isLoading,
    user,
    pendingTwoFactor,
    twoFactorVerified,
    verify2FA,
    isAdmin
  } = useAuth();

  // Handle 2FA verification
  const handle2FAVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    setTwoFactorLoading(true);
    setError("");

    try {
      await verify2FA(twoFactorCode);

      // 2FA verification successful
      toast({
        title: "2FA Verified",
        description: "Welcome to the admin panel!",
      });

      setTwoFactorCode("");
    } catch (err: any) {
      setError(err instanceof Error ? err.message : "2FA verification failed");
      toast({
        title: "Verification Failed",
        description: err instanceof Error ? err.message : "2FA verification failed",
        variant: "destructive",
      });
    } finally {
      setTwoFactorLoading(false);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-forest/10 to-earth/10">
        <Card className="w-96">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-forest mb-4" />
            <p className="text-gray-600">Checking authentication...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Not authenticated - show login
  if (!isAuthenticated || !isAdmin()) {
    return (
      <AdminLogin
        onLoginSuccess={() => {
          // Auth context will handle the state update
        }}
      />
    );
  }

  // Pending 2FA verification
  if (pendingTwoFactor && !twoFactorVerified) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-forest/10 to-earth/10">
        <Card className="w-full max-w-md">
          <CardContent className="py-8">
            <div className="text-center mb-6">
              <div className="mx-auto mb-4 w-12 h-12 bg-forest rounded-full flex items-center justify-center">
                <Smartphone className="w-6 h-6 text-white" />
              </div>
              <h2 className="text-xl font-bold text-forest mb-2">Two-Factor Authentication</h2>
              <p className="text-gray-600 text-center mb-2">
                Please enter your 6-digit authentication code
              </p>
              <p className="text-sm text-gray-500 text-center">
                Use your authenticator app to generate the code
              </p>
            </div>

            <form onSubmit={handle2FAVerification} className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="twoFactorCode">Authentication Code</Label>
                <div className="relative">
                  <Smartphone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="twoFactorCode"
                    type="text"
                    value={twoFactorCode}
                    onChange={(e) => setTwoFactorCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                    placeholder="000000"
                    className="pl-10 text-center text-lg tracking-widest"
                    maxLength={6}
                    required
                    disabled={twoFactorLoading}
                    autoComplete="one-time-code"
                  />
                </div>
                <p className="text-xs text-gray-500 text-center">
                  Enter the 6-digit code from Google Authenticator
                </p>
              </div>

              <Button
                type="submit"
                className="w-full bg-forest hover:bg-forest/90"
                disabled={twoFactorLoading || twoFactorCode.length !== 6}
              >
                {twoFactorLoading ? "Verifying..." : "Verify & Continue"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Show 2FA setup if requested
  if (showTwoFactorSetup) {
    return (
      <TwoFactorSetup
        onSetupComplete={() => {
          setShowTwoFactorSetup(false);
          // Auth context will handle the state update
        }}
        onCancel={() => setShowTwoFactorSetup(false)}
      />
    );
  }

  // Check if admin needs to set up 2FA (first time login)
  if (isAuthenticated && isAdmin() && !twoFactorVerified) {
    // Check if user has never set up 2FA
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-forest/10 to-earth/10">
        <Card className="w-full max-w-md">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Shield className="h-12 w-12 text-orange-500 mb-4" />
            <h2 className="text-xl font-bold text-forest mb-2">Setup Two-Factor Authentication</h2>
            <p className="text-gray-600 text-center mb-4">
              For enhanced security, we recommend setting up two-factor authentication for your admin account.
            </p>
            <div className="flex gap-3 w-full">
              <button
                onClick={() => setShowTwoFactorSetup(true)}
                className="flex-1 bg-forest text-white px-4 py-2 rounded-lg hover:bg-forest/90 transition-colors"
              >
                Setup 2FA Now
              </button>
              <button
                onClick={() => {
                  // Skip 2FA setup for now, but show reminder
                  // Auth context will handle the state
                }}
                className="flex-1 bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"
              >
                Skip for Now
              </button>
            </div>
            <p className="text-xs text-gray-500 text-center mt-3">
              You can set up 2FA later from the security dashboard
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Authenticated and verified - show admin content
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Security status bar */}
      <div className={`${twoFactorVerified ? 'bg-green-600' : 'bg-orange-500'} text-white px-4 py-2 text-sm`}>
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Shield className="h-4 w-4" />
            <span>
              {twoFactorVerified ? 'Secure Admin Session Active' : 'Admin Session Active (2FA Recommended)'}
            </span>
          </div>
          <div className="flex items-center space-x-4">
            {!twoFactorVerified && (
              <button
                onClick={() => setShowTwoFactorSetup(true)}
                className="text-orange-100 hover:text-white underline text-xs font-medium"
              >
                Setup 2FA for Enhanced Security
              </button>
            )}
            <span className="text-green-100 text-xs">
              Session expires in 4 hours
            </span>
          </div>
        </div>
      </div>

      {/* Admin content */}
      {children}
    </div>
  );
}

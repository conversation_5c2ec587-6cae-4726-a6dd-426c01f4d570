import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  User, 
  Globe,
  Smartphone,
  Key,
  Activity
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";

interface SecurityEvent {
  id: number;
  eventType: string;
  ipAddress: string;
  userAgent?: string;
  severity: string;
  createdAt: string;
  details?: any;
}

interface SecurityDashboardProps {
  onSetup2FA: () => void;
}

export function SecurityDashboard({ onSetup2FA }: SecurityDashboardProps) {
  const [user, setUser] = useState<any>(null);

  // Fetch current user info
  const { data: authStatus } = useQuery({
    queryKey: ['/api/auth/status'],
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Fetch security logs
  const { data: securityLogs = [], isLoading: logsLoading } = useQuery<SecurityEvent[]>({
    queryKey: ['/api/admin/security-logs'],
    refetchInterval: 60000, // Refresh every minute
  });

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getEventIcon = (eventType: string) => {
    switch (eventType) {
      case 'LOGIN_SUCCESS': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'LOGIN_FAILED': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case '2FA_VERIFIED': return <Shield className="h-4 w-4 text-green-600" />;
      case '2FA_FAILED': return <Shield className="h-4 w-4 text-red-600" />;
      case 'LOGOUT': return <User className="h-4 w-4 text-blue-600" />;
      default: return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const formatEventType = (eventType: string) => {
    return eventType.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const recentLogs = securityLogs.slice(0, 10);
  const criticalEvents = securityLogs.filter(log => log.severity === 'CRITICAL').length;
  const highEvents = securityLogs.filter(log => log.severity === 'HIGH').length;
  const failedLogins = securityLogs.filter(log => log.eventType === 'LOGIN_FAILED').length;

  return (
    <div className="space-y-6">
      {/* Security Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Shield className="h-8 w-8 text-green-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">2FA Status</p>
                <p className="text-lg font-bold text-green-600">
                  {authStatus?.twoFactorVerified ? 'Enabled' : 'Disabled'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-8 w-8 text-red-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Critical Events</p>
                <p className="text-lg font-bold text-red-600">{criticalEvents}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-8 w-8 text-orange-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">High Priority</p>
                <p className="text-lg font-bold text-orange-600">{highEvents}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <User className="h-8 w-8 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-muted-foreground">Failed Logins</p>
                <p className="text-lg font-bold text-blue-600">{failedLogins}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Security Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security Recommendations
          </CardTitle>
          <CardDescription>
            Improve your account security with these recommendations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!authStatus?.twoFactorVerified && (
            <Alert>
              <Smartphone className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>Enable two-factor authentication for enhanced security</span>
                <Button size="sm" onClick={onSetup2FA} className="bg-forest hover:bg-forest/90">
                  Setup 2FA
                </Button>
              </AlertDescription>
            </Alert>
          )}

          <Alert>
            <Key className="h-4 w-4" />
            <AlertDescription>
              <strong>Strong Password:</strong> Ensure your password is at least 12 characters long with mixed case, numbers, and symbols.
            </AlertDescription>
          </Alert>

          <Alert>
            <Globe className="h-4 w-4" />
            <AlertDescription>
              <strong>Secure Connection:</strong> Always access the admin panel from a secure network and trusted devices.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>

      {/* Recent Security Events */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Recent Security Events
          </CardTitle>
          <CardDescription>
            Latest security-related activities on your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          {logsLoading ? (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-forest mx-auto"></div>
              <p className="text-sm text-muted-foreground mt-2">Loading security events...</p>
            </div>
          ) : recentLogs.length === 0 ? (
            <div className="text-center py-8">
              <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">No security events recorded</p>
            </div>
          ) : (
            <div className="space-y-3">
              {recentLogs.map((event) => (
                <div key={event.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getEventIcon(event.eventType)}
                    <div>
                      <p className="font-medium text-sm">
                        {formatEventType(event.eventType)}
                      </p>
                      <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                        <Globe className="h-3 w-3" />
                        <span>{event.ipAddress}</span>
                        <Clock className="h-3 w-3 ml-2" />
                        <span>{new Date(event.createdAt).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                  <Badge className={getSeverityColor(event.severity)}>
                    {event.severity}
                  </Badge>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Security Tips */}
      <Card>
        <CardHeader>
          <CardTitle>Security Best Practices</CardTitle>
          <CardDescription>
            Follow these guidelines to keep your admin account secure
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              <h4 className="font-medium">Account Security</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Use a unique, strong password</li>
                <li>• Enable two-factor authentication</li>
                <li>• Log out when finished</li>
                <li>• Don't share your credentials</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h4 className="font-medium">Access Security</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Use secure, trusted networks</li>
                <li>• Keep your browser updated</li>
                <li>• Clear browser data regularly</li>
                <li>• Monitor login activities</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

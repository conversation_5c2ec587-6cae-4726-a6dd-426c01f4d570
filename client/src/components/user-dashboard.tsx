import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, <PERSON>Title } from "./ui/card";
import { <PERSON><PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { useAuth } from "./auth-context";
import { AdminOnly, UserOnly, AuthenticatedOnly, usePermissions } from "./role-based-visibility";
import { Shield, User, ShoppingBag, MessageSquare, Settings, BarChart3 } from "lucide-react";
import { Link } from "wouter";

export function UserDashboard() {
  const { user } = useAuth();
  const { isAdmin, isUser, canAccessAdmin } = usePermissions();

  if (!user) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Please log in to view your dashboard.</p>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-forest to-forest-dark text-white rounded-lg p-6">
        <h1 className="text-3xl font-bold mb-2">
          Welcome back, {user.firstName}!
        </h1>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="bg-white/20 text-white">
            {user.role === "admin" ? (
              <>
                <Shield className="h-3 w-3 mr-1" />
                Administrator
              </>
            ) : (
              <>
                <User className="h-3 w-3 mr-1" />
                User
              </>
            )}
          </Badge>
          <span className="text-white/80">•</span>
          <span className="text-white/80">
            Last login: {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : "First time"}
          </span>
        </div>
      </div>

      {/* Quick Actions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        
        {/* User Actions */}
        <AuthenticatedOnly>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShoppingBag className="h-5 w-5" />
                Shopping
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/products">
                <Button variant="outline" className="w-full justify-start">
                  Browse Products
                </Button>
              </Link>
              <Link href="/checkout">
                <Button variant="outline" className="w-full justify-start">
                  View Cart & Checkout
                </Button>
              </Link>
            </CardContent>
          </Card>
        </AuthenticatedOnly>

        <AuthenticatedOnly>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Support
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/contact">
                <Button variant="outline" className="w-full justify-start">
                  Contact Support
                </Button>
              </Link>
              <UserOnly>
                <Button variant="outline" className="w-full justify-start">
                  View My Orders
                </Button>
              </UserOnly>
            </CardContent>
          </Card>
        </AuthenticatedOnly>

        {/* Admin Actions */}
        <AdminOnly>
          <Card className="border-orange-200 bg-orange-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-orange-800">
                <Shield className="h-5 w-5" />
                Admin Panel
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/admin">
                <Button variant="outline" className="w-full justify-start">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  Dashboard
                </Button>
              </Link>
              <Link href="/admin/orders">
                <Button variant="outline" className="w-full justify-start">
                  Manage Orders
                </Button>
              </Link>
              <Link href="/admin/contacts">
                <Button variant="outline" className="w-full justify-start">
                  View Inquiries
                </Button>
              </Link>
            </CardContent>
          </Card>
        </AdminOnly>

        <AdminOnly>
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-800">
                <Settings className="h-5 w-5" />
                System Management
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start">
                User Management
              </Button>
              <Button variant="outline" className="w-full justify-start">
                System Settings
              </Button>
              <Button variant="outline" className="w-full justify-start">
                Analytics
              </Button>
            </CardContent>
          </Card>
        </AdminOnly>
      </div>

      {/* Role-specific Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <UserOnly>
          <Card>
            <CardHeader>
              <CardTitle>Your Account</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p><strong>Username:</strong> {user.username}</p>
                <p><strong>Email:</strong> {user.email}</p>
                <p><strong>Account Status:</strong> 
                  <Badge variant={user.isActive ? "default" : "destructive"} className="ml-2">
                    {user.isActive ? "Active" : "Inactive"}
                  </Badge>
                </p>
                <p><strong>Email Verified:</strong> 
                  <Badge variant={user.isEmailVerified ? "default" : "secondary"} className="ml-2">
                    {user.isEmailVerified ? "Verified" : "Pending"}
                  </Badge>
                </p>
              </div>
            </CardContent>
          </Card>
        </UserOnly>

        <AdminOnly>
          <Card>
            <CardHeader>
              <CardTitle>Admin Privileges</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <p className="text-sm text-gray-600">You have administrative access to:</p>
                <ul className="list-disc list-inside space-y-1 text-sm">
                  <li>View and manage all orders</li>
                  <li>Access customer inquiries</li>
                  <li>Manage product catalog</li>
                  <li>View system analytics</li>
                  <li>User account management</li>
                </ul>
                <div className="pt-2">
                  <Badge variant="outline" className="text-orange-600 border-orange-600">
                    Administrator Role
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </AdminOnly>
      </div>

      {/* Debug Information (Admin Only) */}
      <AdminOnly>
        <Card className="border-gray-200 bg-gray-50">
          <CardHeader>
            <CardTitle className="text-sm text-gray-600">Debug Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xs text-gray-500 space-y-1">
              <p><strong>User ID:</strong> {user.id}</p>
              <p><strong>Role:</strong> {user.role}</p>
              <p><strong>Can Access Admin:</strong> {canAccessAdmin ? "Yes" : "No"}</p>
              <p><strong>Is Admin:</strong> {isAdmin ? "Yes" : "No"}</p>
              <p><strong>Is User:</strong> {isUser ? "Yes" : "No"}</p>
              <p><strong>Created:</strong> {new Date(user.createdAt).toLocaleString()}</p>
            </div>
          </CardContent>
        </Card>
      </AdminOnly>
    </div>
  );
}

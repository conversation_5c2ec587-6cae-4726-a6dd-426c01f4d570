import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import type { User } from "@shared/schema";

interface AuthContextType {
  // Authentication state
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;

  // Authentication methods
  login: (username: string, password: string) => Promise<void>;
  register: (userData: {
    username: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  }) => Promise<void>;
  logout: () => Promise<void>;

  // Role checking
  isAdmin: () => boolean;
  isUser: () => boolean;
  hasRole: (role: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const queryClient = useQueryClient();

  // Check authentication status on mount
  const { data: authStatus, isLoading } = useQuery({
    queryKey: ["/api/auth/status"],
    queryFn: async () => {
      const response = await fetch("/api/auth/status", {
        credentials: "include",
      });
      if (response.ok) {
        return await response.json();
      }
      return { authenticated: false, user: null };
    },
    retry: false,
  });

  useEffect(() => {
    if (authStatus) {
      setIsAuthenticated(authStatus.authenticated);
      setUser(authStatus.user || null);
    }
  }, [authStatus]);

  const loginMutation = useMutation({
    mutationFn: async ({ username, password }: { username: string; password: string }) => {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Login failed");
      }

      return response.json();
    },
    onSuccess: (data) => {
      setIsAuthenticated(true);
      setUser(data.user || null);
      queryClient.invalidateQueries({ queryKey: ["/api/auth"] });
      queryClient.invalidateQueries({ queryKey: ["/api/admin"] });
    },
  });

  const registerMutation = useMutation({
    mutationFn: async (userData: {
      username: string;
      email: string;
      password: string;
      firstName: string;
      lastName: string;
    }) => {
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Registration failed");
      }

      return response.json();
    },
    onSuccess: (data) => {
      setIsAuthenticated(true);
      setUser(data.user || null);
      queryClient.invalidateQueries({ queryKey: ["/api/auth"] });
    },
  });

  const logoutMutation = useMutation({
    mutationFn: async () => {
      const response = await fetch("/api/auth/logout", {
        method: "POST",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Logout failed");
      }

      return response.json();
    },
    onSuccess: () => {
      setIsAuthenticated(false);
      setUser(null);
      queryClient.clear();
    },
  });

  const login = async (username: string, password: string) => {
    const result = await loginMutation.mutateAsync({ username, password });
    return result;
  };

  const register = async (userData: {
    username: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  }) => {
    const result = await registerMutation.mutateAsync(userData);
    return result;
  };

  const logout = async () => {
    await logoutMutation.mutateAsync();
  };

  // Role checking methods
  const isAdmin = () => user?.role === "admin";
  const isUser = () => user?.role === "user";
  const hasRole = (role: string) => user?.role === role;

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isLoading,
        user,
        login,
        register,
        logout,
        isAdmin,
        isUser,
        hasRole,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

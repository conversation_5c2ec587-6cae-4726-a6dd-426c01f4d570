import { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import type { User } from "@shared/schema";

interface AuthContextType {
  // Authentication state
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;

  // 2FA state
  requiresTwoFactor: boolean;
  twoFactorVerified: boolean;
  pendingTwoFactor: boolean;
  twoFactorEnabled: boolean; // Database 2FA enabled status

  // Authentication methods
  login: (username: string, password: string) => Promise<{ requiresTwoFactor?: boolean }>;
  verify2FA: (token: string) => Promise<void>;
  register: (userData: {
    username: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  }) => Promise<void>;
  logout: () => Promise<void>;

  // Utility methods
  refetchAuth: () => Promise<void>;

  // Role checking
  isAdmin: () => boolean;
  isUser: () => boolean;
  hasRole: (role: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<User | null>(null);
  const [requiresTwoFactor, setRequiresTwoFactor] = useState(false);
  const [twoFactorVerified, setTwoFactorVerified] = useState(false);
  const [pendingTwoFactor, setPendingTwoFactor] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const queryClient = useQueryClient();

  // Check authentication status on mount
  const { data: authStatus, isLoading } = useQuery({
    queryKey: ["/api/auth/status"],
    queryFn: async () => {
      const response = await fetch("/api/auth/status", {
        credentials: "include",
      });
      if (response.ok) {
        return await response.json();
      }
      return {
        authenticated: false,
        user: null,
        isAdmin: false,
        twoFactorVerified: false,
        pendingTwoFactor: false,
        requiresTwoFactor: false,
        twoFactorEnabled: false
      };
    },
    retry: false,
  });

  useEffect(() => {
    if (authStatus) {
      setIsAuthenticated(authStatus.authenticated);
      setUser(authStatus.user || null);
      setTwoFactorVerified(authStatus.twoFactorVerified || false);
      setPendingTwoFactor(authStatus.pendingTwoFactor || false);
      setRequiresTwoFactor(authStatus.requiresTwoFactor || false);
      setTwoFactorEnabled(authStatus.twoFactorEnabled || false);
    }
  }, [authStatus]);

  const loginMutation = useMutation({
    mutationFn: async ({ username, password }: { username: string; password: string }) => {
      // Get CSRF token first
      const csrfResponse = await fetch("/api/auth/csrf-token", {
        credentials: "include",
      });
      const { csrfToken } = await csrfResponse.json();

      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": csrfToken,
        },
        credentials: "include",
        body: JSON.stringify({ username, password }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Login failed");
      }

      return response.json();
    },
    onSuccess: (data) => {
      if (data.requiresTwoFactor) {
        // 2FA required - user is authenticated but needs 2FA verification
        setIsAuthenticated(true); // User is logged in, just needs 2FA
        setUser(data.user || null);
        setRequiresTwoFactor(true);
        setPendingTwoFactor(true);
        setTwoFactorVerified(false);
      } else {
        // Login complete
        setIsAuthenticated(true);
        setUser(data.user || null);
        setRequiresTwoFactor(false);
        setPendingTwoFactor(false);
        setTwoFactorVerified(true);
      }
      queryClient.invalidateQueries({ queryKey: ["/api/auth/status"] });
      queryClient.invalidateQueries({ queryKey: ["/api/admin"] });
    },
  });

  const verify2FAMutation = useMutation({
    mutationFn: async (token: string) => {
      // Get CSRF token first
      const csrfResponse = await fetch("/api/auth/csrf-token", {
        credentials: "include",
      });
      const { csrfToken } = await csrfResponse.json();

      const response = await fetch("/api/auth/verify-2fa", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": csrfToken,
        },
        credentials: "include",
        body: JSON.stringify({ token }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "2FA verification failed");
      }

      return response.json();
    },
    onSuccess: (data) => {
      // 2FA verification successful
      setIsAuthenticated(true);
      setUser(data.user || null);
      setRequiresTwoFactor(false);
      setPendingTwoFactor(false);
      setTwoFactorVerified(true);
      queryClient.invalidateQueries({ queryKey: ["/api/auth/status"] });
      queryClient.invalidateQueries({ queryKey: ["/api/admin"] });
    },
  });

  const registerMutation = useMutation({
    mutationFn: async (userData: {
      username: string;
      email: string;
      password: string;
      firstName: string;
      lastName: string;
    }) => {
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Registration failed");
      }

      return response.json();
    },
    onSuccess: (data) => {
      setIsAuthenticated(true);
      setUser(data.user || null);
      queryClient.invalidateQueries({ queryKey: ["/api/auth"] });
    },
  });

  const logoutMutation = useMutation({
    mutationFn: async () => {
      // Get CSRF token first
      const csrfResponse = await fetch("/api/auth/csrf-token", {
        credentials: "include",
      });

      if (!csrfResponse.ok) {
        throw new Error("Failed to get security token");
      }

      const { csrfToken } = await csrfResponse.json();

      // Then make logout request with CSRF token
      const response = await fetch("/api/auth/logout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": csrfToken,
        },
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Logout failed");
      }

      return response.json();
    },
    onSuccess: () => {
      setIsAuthenticated(false);
      setUser(null);
      setRequiresTwoFactor(false);
      setTwoFactorVerified(false);
      setPendingTwoFactor(false);
      setTwoFactorEnabled(false);
      queryClient.clear();
    },
  });

  const login = async (username: string, password: string) => {
    const result = await loginMutation.mutateAsync({ username, password });
    return result;
  };

  const register = async (userData: {
    username: string;
    email: string;
    password: string;
    firstName: string;
    lastName: string;
  }) => {
    const result = await registerMutation.mutateAsync(userData);
    return result;
  };

  const verify2FA = async (token: string) => {
    await verify2FAMutation.mutateAsync(token);
  };

  const logout = async () => {
    await logoutMutation.mutateAsync();
  };

  const refetchAuth = async () => {
    await queryClient.invalidateQueries({ queryKey: ["/api/auth/status"] });
  };

  // Role checking methods
  const isAdmin = () => user?.role === "admin";
  const isUser = () => user?.role === "user";
  const hasRole = (role: string) => user?.role === role;

  return (
    <AuthContext.Provider
      value={{
        isAuthenticated,
        isLoading,
        user,
        requiresTwoFactor,
        twoFactorVerified,
        pendingTwoFactor,
        twoFactorEnabled,
        login,
        verify2FA,
        register,
        logout,
        refetchAuth,
        isAdmin,
        isUser,
        hasRole,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

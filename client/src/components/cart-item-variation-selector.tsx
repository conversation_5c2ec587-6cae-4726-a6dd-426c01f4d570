import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { AlertCircle, Package, Ruler, Palette, Star, Check } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useCart } from "./cart-context";
import { cn } from "@/lib/utils";
import type { ProductVariation, CartItem, Product } from "@shared/schema";

interface CartItemVariationSelectorProps {
  cartItem: CartItem & { product: Product };
  className?: string;
}

export function CartItemVariationSelector({ cartItem, className }: CartItemVariationSelectorProps) {
  const [selectedVariations, setSelectedVariations] = useState<Record<string, ProductVariation>>({});
  const [isUpdating, setIsUpdating] = useState(false);
  const { toast } = useToast();
  const { updateQuantity, removeFromCart } = useCart();
  const queryClient = useQueryClient();

  const { data: variations = [], isLoading } = useQuery<ProductVariation[]>({
    queryKey: [`/api/products/${cartItem.productId}/variations`],
    enabled: !!cartItem.productId,
  });

  // Group variations by type
  const variationsByType = variations.reduce((acc, variation) => {
    if (!acc[variation.type]) {
      acc[variation.type] = [];
    }
    acc[variation.type].push(variation);
    return acc;
  }, {} as Record<string, ProductVariation[]>);

  // Sort variations within each type by sortOrder
  Object.keys(variationsByType).forEach(type => {
    variationsByType[type].sort((a, b) => a.sortOrder - b.sortOrder);
  });

  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'size':
        return <Ruler className="h-4 w-4" />;
      case 'color':
        return <Palette className="h-4 w-4" />;
      case 'model':
        return <Package className="h-4 w-4" />;
      default:
        return <Star className="h-4 w-4" />;
    }
  };

  const getTypeLabel = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  const handleVariationSelect = (type: string, variationId: string) => {
    const variation = variations.find(v => v.id.toString() === variationId);
    if (variation) {
      setSelectedVariations(prev => ({
        ...prev,
        [type]: variation
      }));
    }
  };

  const allVariationTypesSelected = () => {
    const requiredTypes = Object.keys(variationsByType);
    return requiredTypes.every(type => selectedVariations[type]);
  };

  const calculateTotalPrice = () => {
    let totalPrice = parseFloat(cartItem.product.price);
    Object.values(selectedVariations).forEach(variation => {
      if (variation.price) {
        totalPrice = parseFloat(variation.price);
      }
    });
    return totalPrice * cartItem.quantity;
  };

  const handleUpdateCartItem = async () => {
    if (!allVariationTypesSelected()) {
      toast({
        title: "Please select all options",
        description: "Please select all product options before updating.",
        variant: "destructive",
      });
      return;
    }

    setIsUpdating(true);
    try {
      // For simplicity, we'll remove the old item and add a new one with variations
      // In a real implementation, you might want to update the existing item
      await removeFromCart(cartItem.productId);
      
      // Add new item with the first selected variation (simplified)
      const firstVariation = Object.values(selectedVariations)[0];
      await updateQuantity(cartItem.productId, cartItem.quantity, firstVariation.id);
      
      toast({
        title: "Cart updated",
        description: "Item has been updated with your selected options.",
      });
      
      queryClient.invalidateQueries({ queryKey: ["/api/cart"] });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update cart item. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  if (isLoading) {
    return (
      <div className={cn("p-4 bg-amber-50 border border-amber-200 rounded-lg", className)}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-32 mb-2"></div>
          <div className="h-8 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (variations.length === 0) {
    return null;
  }

  return (
    <Card className={cn("border-amber-200 bg-amber-50", className)}>
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Warning Header */}
          <div className="flex items-start gap-2">
            <AlertCircle className="h-5 w-5 text-amber-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-amber-900">Options Required</h4>
              <p className="text-sm text-amber-700">
                Please select options for "{cartItem.product.name}" to complete your enquiry.
              </p>
            </div>
          </div>

          {/* Variation Selectors */}
          {Object.entries(variationsByType).map(([type, typeVariations]) => (
            <div key={type} className="space-y-2">
              <label className="flex items-center gap-2 text-sm font-medium text-gray-900">
                {getTypeIcon(type)}
                Select {getTypeLabel(type)}
                <span className="text-red-500">*</span>
              </label>
              
              <Select onValueChange={(value) => handleVariationSelect(type, value)}>
                <SelectTrigger className="w-full bg-white">
                  <SelectValue placeholder={`Choose ${type}...`} />
                </SelectTrigger>
                <SelectContent>
                  {typeVariations
                    .filter(variation => variation.isActive)
                    .map((variation) => (
                      <SelectItem 
                        key={variation.id} 
                        value={variation.id.toString()}
                        disabled={variation.stockQuantity === 0}
                      >
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{variation.name}</span>
                            <span className="text-sm text-gray-500">({variation.value})</span>
                          </div>
                          <div className="flex items-center gap-2 ml-4">
                            {variation.price && (
                              <span className="text-sm font-medium text-forest">
                                ₹{parseFloat(variation.price).toFixed(2)}
                              </span>
                            )}
                            <Badge 
                              variant={variation.stockQuantity > 0 ? "secondary" : "destructive"}
                              className="text-xs"
                            >
                              {variation.stockQuantity > 0 ? `${variation.stockQuantity} left` : "Out of stock"}
                            </Badge>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          ))}

          {/* Selected Options Summary */}
          {Object.keys(selectedVariations).length > 0 && (
            <div className="bg-white rounded-lg p-3 border">
              <h5 className="font-medium text-gray-900 mb-2">Selected Options:</h5>
              <div className="space-y-1">
                {Object.entries(selectedVariations).map(([type, variation]) => (
                  <div key={type} className="flex items-center gap-2 text-sm">
                    <Check className="h-3 w-3 text-green-600" />
                    <span className="capitalize font-medium">{type}:</span>
                    <span>{variation.name} ({variation.value})</span>
                    {variation.price && (
                      <span className="text-forest font-medium">
                        ₹{parseFloat(variation.price).toFixed(2)}
                      </span>
                    )}
                  </div>
                ))}
              </div>
              
              {allVariationTypesSelected() && (
                <div className="mt-3 pt-3 border-t">
                  <div className="flex justify-between items-center">
                    <span className="font-medium text-gray-900">
                      Total ({cartItem.quantity} items):
                    </span>
                    <span className="text-lg font-bold text-forest">
                      ₹{calculateTotalPrice().toFixed(2)}
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Update Button */}
          <Button
            onClick={handleUpdateCartItem}
            disabled={!allVariationTypesSelected() || isUpdating}
            className="w-full bg-forest hover:bg-forest/90"
          >
            {isUpdating ? "Updating..." : "Update Cart Item"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { 
  Shield, 
  Smartphone, 
  Copy, 
  Check, 
  AlertTriangle, 
  Download,
  Eye,
  EyeOff
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface TwoFactorSetupProps {
  onSetupComplete: () => void;
  onCancel: () => void;
}

export function TwoFactorSetup({ onSetupComplete, onCancel }: TwoFactorSetupProps) {
  const [step, setStep] = useState<'setup' | 'verify' | 'backup'>('setup');
  const [qrCode, setQrCode] = useState('');
  const [manualK<PERSON>, setManualKey] = useState('');
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [verificationCode, setVerificationCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [copied, setCopied] = useState(false);
  const [showBackupCodes, setShowBackupCodes] = useState(false);
  const [csrfToken, setCsrfToken] = useState('');
  const { toast } = useToast();

  // Get CSRF token on component mount
  useEffect(() => {
    fetch('/api/auth/csrf-token', { credentials: 'include' })
      .then(res => res.json())
      .then(data => setCsrfToken(data.csrfToken))
      .catch(console.error);
  }, []);

  const initiate2FASetup = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/setup-2fa', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken,
        },
        credentials: 'include',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || '2FA setup failed');
      }

      setQrCode(data.qrCode);
      setManualKey(data.manualEntryKey);
      setBackupCodes(data.backupCodes);
      setStep('verify');
    } catch (error) {
      setError(error instanceof Error ? error.message : '2FA setup failed');
    } finally {
      setLoading(false);
    }
  };

  const verify2FA = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/enable-2fa', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({
          token: verificationCode,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || '2FA verification failed');
      }

      setStep('backup');
      toast({
        title: "2FA Enabled",
        description: "Two-factor authentication has been successfully enabled!",
      });
    } catch (error) {
      setError(error instanceof Error ? error.message : '2FA verification failed');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      toast({
        title: "Copied",
        description: "Text copied to clipboard",
      });
    } catch (error) {
      toast({
        title: "Copy failed",
        description: "Please copy the text manually",
        variant: "destructive",
      });
    }
  };

  const downloadBackupCodes = () => {
    const content = `EcoGrovea Admin - Two-Factor Authentication Backup Codes
Generated: ${new Date().toLocaleString()}

IMPORTANT: Store these codes in a safe place. Each code can only be used once.

${backupCodes.map((code, index) => `${index + 1}. ${code}`).join('\n')}

Instructions:
- Use these codes if you lose access to your authenticator app
- Each code can only be used once
- Generate new codes if you use all of them
- Keep these codes secure and private`;

    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ecogrovea-2fa-backup-codes-${Date.now()}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-forest/10 to-earth/10 p-4">
      <Card className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 w-12 h-12 bg-forest rounded-full flex items-center justify-center">
            <Shield className="w-6 h-6 text-white" />
          </div>
          <CardTitle className="text-2xl font-bold text-forest">
            Two-Factor Authentication Setup
          </CardTitle>
          <CardDescription>
            {step === 'setup' && 'Enhance your account security with 2FA'}
            {step === 'verify' && 'Scan the QR code and verify your setup'}
            {step === 'backup' && 'Save your backup codes'}
          </CardDescription>
        </CardHeader>

        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {step === 'setup' && (
            <div className="space-y-6">
              <div className="text-center">
                <Smartphone className="mx-auto h-16 w-16 text-forest mb-4" />
                <h3 className="text-lg font-semibold mb-2">Secure Your Admin Account</h3>
                <p className="text-muted-foreground mb-4">
                  Two-factor authentication adds an extra layer of security to your admin account.
                  You'll need an authenticator app like Google Authenticator or Authy.
                </p>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">What you'll need:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• An authenticator app (Google Authenticator, Authy, etc.)</li>
                  <li>• Your smartphone or tablet</li>
                  <li>• A secure place to store backup codes</li>
                </ul>
              </div>

              <div className="flex gap-3">
                <Button
                  onClick={initiate2FASetup}
                  disabled={loading}
                  className="flex-1 bg-forest hover:bg-forest/90"
                >
                  {loading ? "Setting up..." : "Start Setup"}
                </Button>
                <Button
                  onClick={onCancel}
                  variant="outline"
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </div>
          )}

          {step === 'verify' && (
            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                <div className="text-center">
                  <h3 className="font-semibold mb-3">1. Scan QR Code</h3>
                  {qrCode && (
                    <div className="bg-white p-4 rounded-lg border inline-block">
                      <img src={qrCode} alt="2FA QR Code" className="w-48 h-48" />
                    </div>
                  )}
                </div>

                <div>
                  <h3 className="font-semibold mb-3">2. Manual Entry (Alternative)</h3>
                  <p className="text-sm text-muted-foreground mb-2">
                    If you can't scan the QR code, enter this key manually:
                  </p>
                  <div className="flex items-center gap-2 p-2 bg-gray-50 rounded border">
                    <code className="flex-1 text-xs break-all">{manualKey}</code>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => copyToClipboard(manualKey)}
                    >
                      {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="font-semibold">3. Enter Verification Code</h3>
                <p className="text-sm text-muted-foreground">
                  Enter the 6-digit code from your authenticator app:
                </p>
                <div className="flex gap-3">
                  <Input
                    type="text"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                    placeholder="000000"
                    maxLength={6}
                    className="text-center text-lg tracking-widest"
                  />
                  <Button
                    onClick={verify2FA}
                    disabled={loading || verificationCode.length !== 6}
                    className="bg-forest hover:bg-forest/90"
                  >
                    {loading ? "Verifying..." : "Verify"}
                  </Button>
                </div>
              </div>
            </div>
          )}

          {step === 'backup' && (
            <div className="space-y-6">
              <div className="text-center">
                <Check className="mx-auto h-16 w-16 text-green-600 mb-4" />
                <h3 className="text-lg font-semibold text-green-600 mb-2">
                  2FA Successfully Enabled!
                </h3>
                <p className="text-muted-foreground">
                  Save these backup codes in a secure location. You can use them to access your account if you lose your authenticator device.
                </p>
              </div>

              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Important:</strong> Each backup code can only be used once. Store them securely and generate new ones if needed.
                </AlertDescription>
              </Alert>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label>Backup Codes</Label>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => setShowBackupCodes(!showBackupCodes)}
                    >
                      {showBackupCodes ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      {showBackupCodes ? "Hide" : "Show"}
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={downloadBackupCodes}
                    >
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </Button>
                  </div>
                </div>

                {showBackupCodes && (
                  <div className="grid grid-cols-2 gap-2 p-4 bg-gray-50 rounded border">
                    {backupCodes.map((code, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Badge variant="secondary" className="font-mono">
                          {code}
                        </Badge>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <Button
                onClick={onSetupComplete}
                className="w-full bg-forest hover:bg-forest/90"
              >
                Complete Setup
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

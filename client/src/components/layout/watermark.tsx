import { cn } from "@/lib/utils";

interface WatermarkProps {
  className?: string;
  opacity?: number;
  size?: "sm" | "md" | "lg";
}

export function Watermark({ className, opacity = 0.08, size = "md" }: WatermarkProps) {
  const sizeClasses = {
    sm: "w-32 h-32",
    md: "w-48 h-48",
    lg: "w-64 h-64"
  };

  return (
    <div
      className={cn(
        "fixed inset-0 pointer-events-none z-[5] flex items-center justify-center",
        className
      )}
      style={{ opacity }}
    >
      <img
        src="https://res.cloudinary.com/dlaz5xqrl/image/upload/v1750689715/eco-dinnerware/branding/ecogrovea-logo.jpg"
        alt="EcoGrovea Watermark"
        className={cn(
          "object-contain select-none",
          sizeClasses[size]
        )}
        draggable={false}
      />
    </div>
  );
}

// Alternative watermark that repeats across the screen
export function RepeatingWatermark({ className, opacity = 0.05 }: WatermarkProps) {
  return (
    <div
      className={cn(
        "fixed inset-0 pointer-events-none z-[5]",
        className
      )}
      style={{
        opacity,
        backgroundImage: `url('https://res.cloudinary.com/dlaz5xqrl/image/upload/v1750689715/eco-dinnerware/branding/ecogrovea-logo.jpg')`,
        backgroundSize: '120px 120px',
        backgroundRepeat: 'repeat',
        backgroundPosition: 'center'
      }}
    />
  );
}

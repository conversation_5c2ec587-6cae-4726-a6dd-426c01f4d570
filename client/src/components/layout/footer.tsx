import { Facebook, Instagram, Phone, Mail, MapPin, MessageCircle } from "lucide-react";
import { <PERSON> } from "wouter";

export function Footer() {
  return (
    <footer className="bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 text-white pt-12 pb-6 relative z-30 overflow-hidden">
      {/* Professional Decorative Elements */}
      <div className="absolute inset-0 opacity-8">
        <div className="absolute top-8 right-8 text-5xl">🌍</div>
        <div className="absolute bottom-8 left-8 text-4xl">🌱</div>
        <div className="absolute top-1/2 right-1/4 text-3xl">✨</div>
        <div className="absolute bottom-1/4 right-8 text-3xl">🍃</div>
      </div>

      {/* Professional gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-blue-800/30 via-transparent to-indigo-800/20"></div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid md:grid-cols-5 gap-6 mb-8">
          <div>
            <div className="flex items-center space-x-3 mb-4">
              <div className="relative">
                <div className="absolute inset-0 bg-green-400 rounded-full blur-lg opacity-40"></div>
                {/* <img
                  src="https://res.cloudinary.com/dlaz5xqrl/image/upload/v1750689715/eco-dinnerware/branding/ecogrovea-logo.jpg"
                  alt="EcoGrovea Logo"
                  className="relative h-10 w-10 object-contain"
                /> */}
              </div>
              <span className="text-xl font-bold font-playfair bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent">EcoGrovea</span>
            </div>
            <p className="text-blue-100 mb-4 leading-relaxed text-sm">
              Leading provider of sustainable, biodegradable dining solutions for environmentally conscious businesses.
            </p>
            <div className="flex space-x-3">
              <a href="#" className="text-blue-200 hover:text-white transition-colors p-1.5 rounded-lg hover:bg-white/10">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="https://www.instagram.com/ecogrovea/" target="_blank" rel="noopener noreferrer" className="text-blue-200 hover:text-white transition-colors p-1.5 rounded-lg hover:bg-white/10">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="https://wa.me/919447176610" target="_blank" rel="noopener noreferrer" className="text-blue-200 hover:text-white transition-colors p-1.5 rounded-lg hover:bg-white/10">
                <MessageCircle className="h-5 w-5" />
              </a>
              {/* <a href="#" className="text-blue-200 hover:text-white transition-colors p-1.5 rounded-lg hover:bg-white/10">
                <Twitter className="h-5 w-5" />
              </a>
              <a href="#" className="text-blue-200 hover:text-white transition-colors p-1.5 rounded-lg hover:bg-white/10">
                <Linkedin className="h-5 w-5" />
              </a> */}
            </div>
          </div>
          
          <div>
            <h3 className="text-lg font-bold mb-4 bg-gradient-to-r from-cyan-300 to-blue-300 bg-clip-text text-transparent">Products</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/products?category=plates" className="text-blue-100 hover:text-cyan-300 transition-colors flex items-center group">
                  <span className="w-2 h-2 bg-cyan-300 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Biodegradable Plates
                </Link>
              </li>
              <li>
                <Link href="/products?category=containers" className="text-blue-100 hover:text-cyan-300 transition-colors flex items-center group">
                  <span className="w-2 h-2 bg-cyan-300 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Compostable Containers
                </Link>
              </li>
              {/* <li>
                <Link href="/products?category=bowls" className="text-blue-100 hover:text-cyan-300 transition-colors flex items-center group">
                  <span className="w-2 h-2 bg-cyan-300 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Bamboo Bowls
                </Link>
              </li> */}
              <li>
                <Link href="/products?category=cutlery" className="text-blue-100 hover:text-cyan-300 transition-colors flex items-center group">
                  <span className="w-2 h-2 bg-cyan-300 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Eco Cutlery
                </Link>
              </li>
              <li>
                <Link href="/products?category=combo" className="text-blue-100 hover:text-cyan-300 transition-colors flex items-center group">
                  <span className="w-2 h-2 bg-cyan-300 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Bundle Packs
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-bold mb-4 bg-gradient-to-r from-indigo-300 to-purple-300 bg-clip-text text-transparent">Company</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about" className="text-blue-100 hover:text-indigo-300 transition-colors flex items-center group">
                  <span className="w-2 h-2 bg-indigo-300 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/about#mission" className="text-blue-100 hover:text-indigo-300 transition-colors flex items-center group">
                  <span className="w-2 h-2 bg-indigo-300 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Our Mission
                </Link>
              </li>
              <li>
                <Link href="/about#sustainability" className="text-blue-100 hover:text-indigo-300 transition-colors flex items-center group">
                  <span className="w-2 h-2 bg-indigo-300 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Sustainability
                </Link>
              </li>
              <li>
                <Link href="/about#certifications" className="text-blue-100 hover:text-indigo-300 transition-colors flex items-center group">
                  <span className="w-2 h-2 bg-indigo-300 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Certifications
                </Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-lg font-bold mb-4 bg-gradient-to-r from-emerald-300 to-teal-300 bg-clip-text text-transparent">Support</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/contact" className="text-blue-100 hover:text-emerald-300 transition-colors flex items-center group">
                  <span className="w-2 h-2 bg-emerald-300 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Contact Us
                </Link>
              </li>
              <li>
                <Link href="/contact#bulk-orders" className="text-blue-100 hover:text-emerald-300 transition-colors flex items-center group">
                  <span className="w-2 h-2 bg-emerald-300 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Bulk Orders
                </Link>
              </li>
              <li>
                <a href="#" className="text-blue-100 hover:text-emerald-300 transition-colors flex items-center group">
                  <span className="w-2 h-2 bg-emerald-300 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Shipping Info
                </a>
              </li>
              <li>
                <a href="#" className="text-blue-100 hover:text-emerald-300 transition-colors flex items-center group">
                  <span className="w-2 h-2 bg-emerald-300 rounded-full mr-3 opacity-0 group-hover:opacity-100 transition-opacity"></span>
                  Returns
                </a>
              </li>
            </ul>
          </div>

          <div>
            <h3 className="text-lg font-bold mb-4 bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">Contact Info</h3>
            <div className="space-y-2 text-blue-100 text-sm">
              <div className="flex items-center space-x-3 hover:text-white transition-colors">
                <Phone className="h-4 w-4 text-cyan-300" />
                <span>+91-9447176610</span>
              </div>
              <div className="flex items-center space-x-3 hover:text-white transition-colors">
                <Mail className="h-4 w-4 text-emerald-300" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 hover:text-white transition-colors">
                <MapPin className="h-4 w-4 text-indigo-300" />
                <span>Aluva,kerala,India</span>
              </div>
              <div className="flex items-center space-x-3 hover:text-white transition-colors text-xs text-blue-200">
                <span className="text-orange-300">🕒</span>
                <span>Mon-Fri: 9AM-6PM IST</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="border-t border-blue-300/20 pt-6 mt-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-blue-200 text-sm">
              &copy; 2025 <span className="bg-gradient-to-r from-green-400 to-emerald-400 bg-clip-text text-transparent font-semibold">EcoGrovea</span>. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="#" className="text-blue-200 hover:text-white text-sm transition-colors hover:underline">
                Privacy Policy
              </a>
              <a href="#" className="text-blue-200 hover:text-white text-sm transition-colors hover:underline">
                Terms of Service
              </a>
              <a href="#" className="text-blue-200 hover:text-white text-sm transition-colors hover:underline">
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}

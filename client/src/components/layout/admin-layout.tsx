import { ReactNode } from "react";
import { Link, useLocation } from "wouter";
import { LayoutDashboard, Users, ShoppingBag, Package, LogOut, Home, Shield } from "lucide-react";
import { useAuth } from "../auth-context";
import { Button } from "../ui/button";
import { useToast } from "@/hooks/use-toast";

interface AdminLayoutProps {
  children: ReactNode;
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const [location] = useLocation();
  const { logout } = useAuth();
  const { toast } = useToast();

  const navItems = [
    { href: "/admin", label: "Dashboard", icon: LayoutDashboard },
    { href: "/admin/orders", label: "Orders", icon: ShoppingBag },
    { href: "/admin/contacts", label: "Contacts", icon: Users },
    { href: "/admin/products", label: "Products", icon: Package },
    { href: "/admin/security", label: "Security", icon: Shield },
  ];

  const handleLogout = async () => {
    try {
      await logout();
      toast({
        title: "Logged out successfully",
        description: "You have been logged out of the admin panel.",
      });
    } catch (error) {
      toast({
        title: "Logout failed",
        description: "There was an error logging out. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <div className="w-64 bg-white shadow-lg border-r border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-forest">Admin Panel</h2>
          <p className="text-sm text-gray-500 mt-1">Management Dashboard</p>
        </div>
        <nav className="p-4">
          <ul className="space-y-1">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = location === item.href;
              return (
                <li key={item.href}>
                  <Link href={item.href}>
                    <a className={`flex items-center px-3 py-2.5 rounded-lg transition-colors ${
                      isActive
                        ? "bg-forest text-white shadow-sm"
                        : "text-gray-700 hover:bg-gray-100 hover:text-forest"
                    }`}>
                      <Icon className="h-5 w-5 mr-3" />
                      <span className="font-medium">{item.label}</span>
                    </a>
                  </Link>
                </li>
              );
            })}
            <li className="pt-4 mt-4 border-t border-gray-200 space-y-1">
              <Link href="/">
                <a className="flex items-center px-3 py-2.5 text-gray-700 hover:bg-gray-100 hover:text-forest rounded-lg transition-colors">
                  <Home className="h-5 w-5 mr-3" />
                  <span className="font-medium">Back to Site</span>
                </a>
              </Link>
              <Button
                variant="ghost"
                onClick={handleLogout}
                className="w-full justify-start px-3 py-2.5 text-gray-700 hover:bg-red-50 hover:text-red-600 h-auto rounded-lg transition-colors"
              >
                <LogOut className="h-5 w-5 mr-3" />
                <span className="font-medium">Logout</span>
              </Button>
            </li>
          </ul>
        </nav>
      </div>
      
      {/* Main content */}
      <div className="flex-1 overflow-auto bg-gray-50">
        <div className="p-6 lg:p-8 min-h-full">
          {children}
        </div>
      </div>
    </div>
  );
}
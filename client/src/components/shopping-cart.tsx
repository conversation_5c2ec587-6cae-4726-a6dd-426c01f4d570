import { useState, useEffect } from "react";
import { useCart } from "./cart-context";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Sheet, Sheet<PERSON>ontent, She<PERSON><PERSON>eader, She<PERSON><PERSON><PERSON><PERSON>, SheetTrigger } from "@/components/ui/sheet";
import { ShoppingCart, Plus, Minus, Trash2 } from "lucide-react";
import { useLocation } from "wouter";

interface ShoppingCartDrawerProps {
  forceOpen?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export function ShoppingCartDrawer({ forceOpen, onOpenChange }: ShoppingCartDrawerProps = {}) {
  const [isOpen, setIsOpen] = useState(false);
  const { items, itemCount, total, updateQuantity, removeFromCart } = useCart();
  const [, setLocation] = useLocation();
  const [prevItemCount, setPrevItemCount] = useState(0);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Track item count changes but don't auto-open cart
  useEffect(() => {
    if (!hasInitialized) {
      // First time setting the item count, don't auto-open
      setPrevItemCount(itemCount);
      setHasInitialized(true);
    } else {
      // Update previous count for other changes (removals, etc.)
      setPrevItemCount(itemCount);
    }
  }, [itemCount, prevItemCount, hasInitialized]);

  const handleCheckout = () => {
    setIsOpen(false);
    setLocation("/checkout");
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="outline" size="icon" className="relative">
          <ShoppingCart className="h-4 w-4" />
          {itemCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs"
            >
              {itemCount}
            </Badge>
          )}
        </Button>
      </SheetTrigger>
      <SheetContent className="w-[400px] sm:w-[540px]">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Enquiry Cart
            {itemCount > 0 && (
              <Badge variant="secondary">{itemCount} items</Badge>
            )}
          </SheetTitle>
        </SheetHeader>

        <div className="flex flex-col h-full">
          {items.length === 0 ? (
            <div className="flex-1 flex flex-col items-center justify-center text-center py-8">
              <ShoppingCart className="h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-muted-foreground mb-6">Your enquiry cart is empty</p>
              <Button
                variant="outline"
                className="mt-2"
                onClick={() => setIsOpen(false)}
              >
                Continue Shopping
              </Button>
            </div>
          ) : (
            <>
              <div className="flex-1 overflow-auto py-4 min-h-0">
                <div className="space-y-4">
                  {items.map((item) => (
                    <div key={`${item.id}-${item.productId}-${(item as any).variationId || 'no-variation'}`} className="p-4 border rounded-lg">
                      <div className="flex items-start space-x-4 mb-3">
                        <img
                          src={item.product.imageUrl || "/placeholder.svg"}
                          alt={item.product.name}
                          className="h-16 w-16 rounded-md object-cover flex-shrink-0"
                        />
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-sm truncate">{item.product.name}</h4>
                          {(item as any).variation && (
                            <p className="text-xs text-muted-foreground">
                              {(item as any).variation.name} • {(item as any).variation.value}
                            </p>
                          )}
                          <p className="text-sm text-muted-foreground">
                            ₹{(item as any).variation?.price || item.product.price} per carton
                          </p>
                          <p className="text-xs text-gray-500">
                            {((item.product as any).unitsPerCarton || 1)} {((item.product as any).unitType || 'pieces')} per carton
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => updateQuantity(item.productId, Math.max(0, item.quantity - 1), (item as any).variationId)}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <span className="w-8 text-center text-sm">{item.quantity}</span>
                          <Button
                            variant="outline"
                            size="icon"
                            className="h-8 w-8"
                            onClick={() => updateQuantity(item.productId, item.quantity + 1, (item as any).variationId)}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                        <Button
                          variant="outline"
                          size="icon"
                          className="h-8 w-8 text-destructive hover:text-destructive"
                          onClick={() => removeFromCart(item.productId, (item as any).variationId)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Fixed bottom section with proper spacing */}
              <div className="flex-shrink-0 border-t bg-background">
                <div className="p-4 space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="font-semibold text-base">Total:</span>
                    <span className="font-semibold text-lg text-forest">₹{total.toFixed(2)}</span>
                  </div>
                  <Separator />
                  <div className="space-y-3 pb-2">
                    <Button
                      className="w-full bg-forest hover:bg-forest/90 text-white h-11 font-medium"
                      onClick={handleCheckout}
                    >
                      Submit Enquiry
                    </Button>
                    <Button
                      variant="outline"
                      className="w-full h-11 font-medium border-forest text-forest hover:bg-forest/5"
                      onClick={() => setIsOpen(false)}
                    >
                      Continue Shopping
                    </Button>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </SheetContent>
    </Sheet>
  );
}
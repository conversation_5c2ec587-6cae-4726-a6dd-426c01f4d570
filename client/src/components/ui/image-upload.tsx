import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Upload, X, Image as ImageIcon, Zap, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { compressImages, getFileSizeInfo } from "@/lib/image-utils";

interface ImageUploadProps {
  onImagesUploaded: (imageUrls: string[], primaryImage: string) => void;
  maxImages?: number;
  existingImages?: string[];
  className?: string;
  autoCompress?: boolean; // Enable automatic compression for optimal performance
  onImageDeleted?: (imageUrl: string) => void; // Callback when an image is deleted from cloud storage
  showDeleteFromCloud?: boolean; // Show delete from cloud storage option
}

export function ImageUpload({
  onImagesUploaded,
  maxImages = 5,
  existingImages = [],
  className = "",
  autoCompress = true,
  onImageDeleted,
  showDeleteFromCloud = false
}: ImageUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [previewImages, setPreviewImages] = useState<string[]>(existingImages);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  // Sync previewImages with existingImages prop when it changes
  useEffect(() => {
    setPreviewImages(existingImages);
  }, [existingImages]);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    // Check file count
    if (previewImages.length + files.length > maxImages) {
      toast({
        title: "Too many images",
        description: `Maximum ${maxImages} images allowed`,
        variant: "destructive",
      });
      return;
    }

    // Validate file types and sizes
    const validFiles: File[] = [];
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      // Check file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Invalid file type",
          description: `${file.name} is not an image file`,
          variant: "destructive",
        });
        continue;
      }

      // Check file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: `${file.name} is larger than 5MB`,
          variant: "destructive",
        });
        continue;
      }

      validFiles.push(file);
    }

    if (validFiles.length === 0) return;

    setUploading(true);

    try {
      // Compress images if enabled (for optimal performance and storage efficiency)
      let filesToUpload = validFiles;
      if (autoCompress) {
        toast({
          title: "Optimizing images...",
          description: "Compressing images for faster upload and optimal performance",
        });

        filesToUpload = await compressImages(validFiles, {
          maxWidth: 800,
          maxHeight: 600,
          quality: 0.8
        });

        // Show compression results
        const originalSize = validFiles.reduce((sum, file) => sum + file.size, 0);
        const compressedSize = filesToUpload.reduce((sum, file) => sum + file.size, 0);
        const savings = Math.round(((originalSize - compressedSize) / originalSize) * 100);

        if (savings > 10) {
          toast({
            title: "Images optimized!",
            description: `Reduced size by ${savings}% - optimized for Cloudinary storage`,
          });
        }
      }

      // Create preview URLs
      const newPreviewUrls = filesToUpload.map(file => URL.createObjectURL(file));
      setPreviewImages(prev => [...prev, ...newPreviewUrls]);

      // Upload files
      const formData = new FormData();
      filesToUpload.forEach(file => {
        formData.append('images', file);
      });

      const response = await fetch('/api/admin/upload-images', {
        method: 'POST',
        credentials: 'include',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Upload failed');
      }

      const result = await response.json();
      
      // Update preview images with actual URLs
      setPreviewImages(prev => {
        const existingCount = prev.length - newPreviewUrls.length;
        const newImages = [...prev.slice(0, existingCount), ...result.imageUrls];
        
        // Call callback with all images
        onImagesUploaded(newImages, newImages[0]);
        
        return newImages;
      });

      // Clean up preview URLs
      newPreviewUrls.forEach(url => URL.revokeObjectURL(url));

      // Show success message with storage info
      const storageType = result.storage === 'cloudinary' ? 'Cloudinary' : 'local storage';
      toast({
        title: "Images uploaded successfully!",
        description: `${validFiles.length} image(s) uploaded to ${storageType}${result.storage === 'cloudinary' ? ' with global CDN' : ''}`,
      });

    } catch (error: any) {
      // Clean up preview URLs on error
      const newPreviewUrls = validFiles.map(file => URL.createObjectURL(file));
      newPreviewUrls.forEach(url => URL.revokeObjectURL(url));
      
      setPreviewImages(prev => prev.slice(0, prev.length - validFiles.length));
      
      toast({
        title: "Upload failed",
        description: error.message || "Failed to upload images",
        variant: "destructive",
      });
    } finally {
      setUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const removeImage = (index: number) => {
    const newImages = previewImages.filter((_, i) => i !== index);
    setPreviewImages(newImages);
    onImagesUploaded(newImages, newImages[0] || '');
  };

  const deleteImageFromCloud = async (imageUrl: string, index: number) => {
    if (!showDeleteFromCloud || !onImageDeleted) return;

    try {
      const response = await fetch('/api/admin/delete-image', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ imageUrl }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete image');
      }

      // Remove from local state
      const newImages = previewImages.filter((_, i) => i !== index);
      setPreviewImages(newImages);
      onImagesUploaded(newImages, newImages[0] || '');

      // Call callback to notify parent component
      onImageDeleted(imageUrl);

      toast({
        title: "Image deleted",
        description: "Image has been deleted from cloud storage",
      });
    } catch (error: any) {
      toast({
        title: "Delete failed",
        description: error.message || "Failed to delete image from cloud storage",
        variant: "destructive",
      });
    }
  };

  const reorderImages = (fromIndex: number, toIndex: number) => {
    const newImages = [...previewImages];
    const [movedImage] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedImage);
    setPreviewImages(newImages);
    onImagesUploaded(newImages, newImages[0]);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div>
        <Label>Product Images</Label>
        <p className="text-sm text-gray-500 mt-1">
          Upload up to {maxImages} images via Cloudinary. First image will be the primary image.
          {autoCompress && " Images are automatically optimized for fast loading and global CDN delivery."}
        </p>
      </div>

      {/* Upload Button */}
      <div className="flex items-center gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          disabled={uploading || previewImages.length >= maxImages}
          className="flex items-center gap-2"
        >
          <Upload className="h-4 w-4" />
          {uploading ? "Uploading..." : "Upload Images"}
        </Button>
        
        <span className="text-sm text-gray-500">
          {previewImages.length} / {maxImages} images
        </span>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        multiple
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Image Previews */}
      {previewImages.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {previewImages.map((imageUrl, index) => (
            <div
              key={index}
              className="relative aspect-square border-2 border-dashed border-gray-200 rounded-lg overflow-hidden group"
            >
              <img
                src={imageUrl}
                alt={`Preview ${index + 1}`}
                className="w-full h-full object-cover"
              />
              
              {/* Primary Image Badge */}
              {index === 0 && (
                <div className="absolute top-2 left-2 bg-forest text-white text-xs px-2 py-1 rounded">
                  Primary
                </div>
              )}
              
              {/* Action Buttons */}
              <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                {/* Remove from list button */}
                <Button
                  type="button"
                  variant="outline"
                  size="icon"
                  className="h-6 w-6 bg-white/90 hover:bg-white"
                  onClick={() => removeImage(index)}
                  title="Remove from list"
                >
                  <X className="h-3 w-3" />
                </Button>

                {/* Delete from cloud storage button */}
                {showDeleteFromCloud && (
                  <Button
                    type="button"
                    variant="destructive"
                    size="icon"
                    className="h-6 w-6"
                    onClick={() => deleteImageFromCloud(imageUrl, index)}
                    title="Delete from cloud storage permanently"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                )}
              </div>
              
              {/* Image Number */}
              <div className="absolute bottom-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                {index + 1}
              </div>
            </div>
          ))}
          
          {/* Add More Button */}
          {previewImages.length < maxImages && (
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              disabled={uploading}
              className="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center text-gray-500 hover:border-gray-400 hover:text-gray-600 transition-colors"
            >
              <ImageIcon className="h-8 w-8 mb-2" />
              <span className="text-sm">Add Image</span>
            </button>
          )}
        </div>
      )}

      {/* Empty State */}
      {previewImages.length === 0 && (
        <div
          onClick={() => fileInputRef.current?.click()}
          className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:border-gray-400 transition-colors"
        >
          <ImageIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
          <p className="text-gray-500 mb-2">Click to upload images</p>
          <p className="text-sm text-gray-400">
            Supports: JPG, PNG, GIF up to 5MB each
          </p>
        </div>
      )}
    </div>
  );
}

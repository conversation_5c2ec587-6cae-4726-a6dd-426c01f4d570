import { useState } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

interface ProductImageGalleryProps {
  images: string[];
  productName: string;
  className?: string;
}

export function ProductImageGallery({
  images,
  productName,
  className = ""
}: ProductImageGalleryProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Handle case where images might be empty or undefined
  const imageList = images && images.length > 0 ? images : [];
  const hasMultipleImages = imageList.length > 1;



  if (imageList.length === 0) {
    return (
      <div className={`aspect-square bg-gray-100 rounded-lg flex items-center justify-center ${className}`}>
        <div className="text-center text-gray-400">
          <div className="w-16 h-16 mx-auto mb-2 bg-gray-200 rounded-lg flex items-center justify-center">
            <span className="text-2xl">📷</span>
          </div>
          <p className="text-sm">No image</p>
        </div>
      </div>
    );
  }

  const currentImage = imageList[currentImageIndex];



  const goToPrevious = () => {
    setCurrentImageIndex((prev) =>
      prev === 0 ? imageList.length - 1 : prev - 1
    );
  };

  const goToNext = () => {
    setCurrentImageIndex((prev) =>
      prev === imageList.length - 1 ? 0 : prev + 1
    );
  };

  const goToImage = (index: number) => {
    setCurrentImageIndex(index);
  };

  return (
    <div className={`relative ${className}`}>
      {/* Main Image Display */}
      <div className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden group">
        <img
          src={currentImage}
          alt={`${productName} - Image ${currentImageIndex + 1}`}
          className="w-full h-full object-cover transition-opacity duration-300"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = '/placeholder-image.jpg'; // Fallback image
          }}
        />

        {/* Navigation Arrows (only show if multiple images) */}
        {hasMultipleImages && (
          <>
            <Button
              variant="outline"
              size="icon"
              className="absolute left-3 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 h-12 w-12 shadow-lg"
              onClick={goToPrevious}
            >
              <ChevronLeft className="h-6 w-6" />
            </Button>

            <Button
              variant="outline"
              size="icon"
              className="absolute right-3 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 h-12 w-12 shadow-lg"
              onClick={goToNext}
            >
              <ChevronRight className="h-6 w-6" />
            </Button>
          </>
        )}

        {/* Image Counter */}
        {hasMultipleImages && (
          <div className="absolute top-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
            {currentImageIndex + 1} / {imageList.length}
          </div>
        )}

        {/* Primary Image Badge */}
        {currentImageIndex === 0 && (
          <div className="absolute top-2 left-2 bg-forest text-white text-xs px-2 py-1 rounded">
            Primary
          </div>
        )}
      </div>

      {/* Thumbnail Navigation (only show if multiple images) */}
      {hasMultipleImages && (
        <div className="flex gap-2 mt-3 overflow-x-auto pb-2">
          {imageList.map((image, index) => (
            <div
              key={index}
              onClick={() => goToImage(index)}
              className={`relative flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200 cursor-pointer ${
                index === currentImageIndex
                  ? 'border-forest shadow-md'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
            >
              <img
                src={image}
                alt={`${productName} thumbnail ${index + 1}`}
                className="w-full h-full object-cover pointer-events-none"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/placeholder-image.jpg'; // Fallback image
                }}
              />
              {index === 0 && (
                <div className="absolute inset-0 flex items-end justify-center pb-1 pointer-events-none">
                  <span className="text-xs text-white bg-black/60 px-1 rounded">
                    Primary
                  </span>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Image Info */}
      <div className="mt-2 text-center">
        <p className="text-xs text-gray-500">
          {hasMultipleImages 
            ? `${imageList.length} images available` 
            : 'Single image'
          }
        </p>
      </div>
    </div>
  );
}

// Compact version for product cards
export function ProductImageGalleryCompact({ 
  images, 
  productName, 
  className = "" 
}: ProductImageGalleryProps) {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const imageList = images && images.length > 0 ? images : [];
  const hasMultipleImages = imageList.length > 1;

  if (imageList.length === 0) {
    return (
      <div className={`aspect-square bg-gray-100 rounded-lg flex items-center justify-center ${className}`}>
        <span className="text-gray-400 text-2xl">📷</span>
      </div>
    );
  }

  const currentImage = imageList[currentImageIndex];

  const goToNext = () => {
    setCurrentImageIndex((prev) => 
      prev === imageList.length - 1 ? 0 : prev + 1
    );
  };

  return (
    <div
      className={`relative bg-gray-100 rounded-lg overflow-hidden group ${hasMultipleImages ? 'cursor-pointer' : ''} ${className}`}
      onClick={hasMultipleImages ? goToNext : undefined}
    >
      <img
        src={currentImage}
        alt={`${productName} - Image ${currentImageIndex + 1}`}
        className="w-full h-full object-cover transition-all duration-300 hover:scale-105"
        onError={(e) => {
          const target = e.target as HTMLImageElement;
          target.src = '/placeholder-image.jpg';
        }}
      />

      {/* Multiple Images Indicator */}
      {hasMultipleImages && (
        <>
          {/* Image counter */}
          <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded-full backdrop-blur-sm">
            <span className="flex items-center gap-1">
              <span>📷</span>
              {currentImageIndex + 1}/{imageList.length}
            </span>
          </div>

          {/* Dot indicators */}
          <div className="absolute bottom-2 left-2 right-2">
            <div className="flex gap-1 justify-center">
              {imageList.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all duration-200 ${
                    index === currentImageIndex
                      ? 'bg-white shadow-lg'
                      : 'bg-white/60'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Hover overlay with instructions */}
          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-200 flex items-center justify-center">
            <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 text-white text-sm bg-black/70 px-3 py-1 rounded-full backdrop-blur-sm">
              <span className="flex items-center gap-1">
                <span>👆</span>
                Click to see more
              </span>
            </div>
          </div>

          {/* Navigation arrows for better UX */}
          <div className="absolute inset-y-0 left-0 right-0 flex items-center justify-between px-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
            <div className="w-6 h-6 bg-black/50 rounded-full flex items-center justify-center text-white text-xs">
              ‹
            </div>
            <div className="w-6 h-6 bg-black/50 rounded-full flex items-center justify-center text-white text-xs">
              ›
            </div>
          </div>
        </>
      )}
    </div>
  );
}

import { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import type { CartItem, Product } from "@shared/schema";

interface CartContextType {
  items: (CartItem & { product: Product })[];
  itemCount: number;
  total: number;
  isLoading: boolean;
  addToCart: (productId: number, quantity: number, variationId?: number) => Promise<void>;
  updateQuantity: (productId: number, quantity: number, variationId?: number) => Promise<void>;
  removeFromCart: (productId: number, variationId?: number) => Promise<void>;
  clearCart: () => Promise<void>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: ReactNode }) {
  const queryClient = useQueryClient();

  const { data: items = [], isLoading } = useQuery<(CartItem & { product: Product })[]>({
    queryKey: ["/api/cart"],
  });

  const addToCartMutation = useMutation({
    mutationFn: async ({ productId, quantity, variationId }: { productId: number; quantity: number; variationId?: number }) => {
      await apiRequest("POST", "/api/cart", { productId, quantity, variationId });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/cart"] });
    },
  });

  const updateQuantityMutation = useMutation({
    mutationFn: async ({ productId, quantity, variationId }: { productId: number; quantity: number; variationId?: number }) => {
      const url = variationId ? `/api/cart/${productId}/${variationId}` : `/api/cart/${productId}`;
      await apiRequest("PUT", url, { quantity });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/cart"] });
    },
  });

  const removeFromCartMutation = useMutation({
    mutationFn: async ({ productId, variationId }: { productId: number; variationId?: number }) => {
      const url = variationId ? `/api/cart/${productId}/${variationId}` : `/api/cart/${productId}`;
      await apiRequest("DELETE", url);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/cart"] });
    },
  });

  const clearCartMutation = useMutation({
    mutationFn: async () => {
      await apiRequest("DELETE", "/api/cart");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/cart"] });
    },
  });

  const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);
  const total = items.reduce((sum, item) => {
    // Use variation price if available, otherwise use product price
    const price = (item as any).variation?.price ? parseFloat((item as any).variation.price) : parseFloat(item.product.price);
    return sum + (price * item.quantity);
  }, 0);

  const addToCart = async (productId: number, quantity: number, variationId?: number) => {
    await addToCartMutation.mutateAsync({ productId, quantity, variationId });
  };

  const updateQuantity = async (productId: number, quantity: number, variationId?: number) => {
    await updateQuantityMutation.mutateAsync({ productId, quantity, variationId });
  };

  const removeFromCart = async (productId: number, variationId?: number) => {
    await removeFromCartMutation.mutateAsync({ productId, variationId });
  };

  const clearCart = async () => {
    await clearCartMutation.mutateAsync();
  };

  return (
    <CartContext.Provider value={{
      items,
      itemCount,
      total,
      isLoading,
      addToCart,
      updateQuantity,
      removeFromCart,
      clearCart,
    }}>
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error("useCart must be used within a CartProvider");
  }
  return context;
}

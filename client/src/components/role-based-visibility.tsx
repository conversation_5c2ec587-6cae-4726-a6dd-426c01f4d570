import { ReactNode } from "react";
import { useAuth } from "./auth-context";

interface RoleBasedVisibilityProps {
  children: ReactNode;
  allowedRoles?: string[];
  requireAuth?: boolean;
  fallback?: ReactNode;
  hideIfNoAccess?: boolean;
}

export function RoleBasedVisibility({ 
  children, 
  allowedRoles = [], 
  requireAuth = false,
  fallback = null,
  hideIfNoAccess = true
}: RoleBasedVisibilityProps) {
  const { isAuthenticated, user, hasRole } = useAuth();

  // Check authentication requirement
  if (requireAuth && !isAuthenticated) {
    return hideIfNoAccess ? null : <>{fallback}</>;
  }

  // Check role-based access
  if (allowedRoles.length > 0) {
    const hasRequiredRole = allowedRoles.some(role => hasRole(role));
    
    if (!hasRequiredRole) {
      return hideIfNoAccess ? null : <>{fallback}</>;
    }
  }

  return <>{children}</>;
}

// Convenience components for common use cases
export function AdminOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleBasedVisibility allowedRoles={["admin"]} fallback={fallback}>
      {children}
    </RoleBasedVisibility>
  );
}

export function UserOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleBasedVisibility allowedRoles={["user"]} fallback={fallback}>
      {children}
    </RoleBasedVisibility>
  );
}

export function AuthenticatedOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleBasedVisibility requireAuth={true} fallback={fallback}>
      {children}
    </RoleBasedVisibility>
  );
}

export function GuestOnly({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  const { isAuthenticated } = useAuth();
  
  if (isAuthenticated) {
    return fallback ? <>{fallback}</> : null;
  }
  
  return <>{children}</>;
}

// Hook for conditional rendering based on permissions
export function usePermissions() {
  const { isAuthenticated, user, hasRole, isAdmin, isUser } = useAuth();
  
  return {
    isAuthenticated,
    user,
    hasRole,
    isAdmin: isAdmin(),
    isUser: isUser(),
    canAccess: (roles: string[]) => roles.some(role => hasRole(role)),
    canAccessAdmin: () => hasRole("admin"),
    canAccessUser: () => hasRole("user") || hasRole("admin"),
  };
}

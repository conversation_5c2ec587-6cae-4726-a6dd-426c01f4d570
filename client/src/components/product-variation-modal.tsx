import { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import { useQuery } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Plus, Minus, Package, Ruler, Palette, Star, AlertCircle, ShoppingCart, MessageSquare } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useCart } from "./cart-context";
import { cn } from "@/lib/utils";
import type { Product, ProductVariation } from "@shared/schema";

interface ProductVariationModalProps {
  product: Product;
  isOpen: boolean;
  onClose: () => void;
  mode?: 'cart' | 'enquiry';
  onAddToCart?: () => void;
  onSubmitEnquiry?: (enquiryData: any) => void;
  initialVariation?: {
    id?: number;
    name?: string;
    type?: string;
    value?: string;
    price?: string;
  };
  initialQuantity?: number;
  isEditMode?: boolean; // New prop to indicate if we're editing existing selections
}

export function ProductVariationModal({
  product,
  isOpen,
  onClose,
  mode = 'cart',
  onAddToCart,
  onSubmitEnquiry,
  initialVariation,
  initialQuantity = 1,
  isEditMode = false
}: ProductVariationModalProps) {
  const [selectedVariations, setSelectedVariations] = useState<Record<string, ProductVariation>>({});
  const [quantity, setQuantity] = useState(initialQuantity);
  const [isAdding, setIsAdding] = useState(false);
  const { addToCart } = useCart();
  const { toast } = useToast();

  const { data: variations = [], isLoading } = useQuery<ProductVariation[]>({
    queryKey: [`/api/products/${product.id}/variations`],
    enabled: !!product.id && isOpen,
  });

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      if (initialVariation && initialVariation.id) {
        // Find the variation in the loaded variations and set it as selected
        const variation = variations.find(v => v.id === initialVariation.id);
        if (variation) {
          setSelectedVariations({
            [variation.type]: variation
          });
        }
      } else {
        setSelectedVariations({});
      }
      setQuantity(initialQuantity);
    }
  }, [isOpen, variations, initialVariation, initialQuantity]);

  // Group variations by type
  const variationsByType = variations.reduce((acc, variation) => {
    if (!acc[variation.type]) {
      acc[variation.type] = [];
    }
    acc[variation.type].push(variation);
    return acc;
  }, {} as Record<string, ProductVariation[]>);

  // Sort variations within each type by sortOrder
  Object.keys(variationsByType).forEach(type => {
    variationsByType[type].sort((a, b) => a.sortOrder - b.sortOrder);
  });

  const getTypeIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'size':
        return <Ruler className="h-4 w-4" />;
      case 'color':
        return <Palette className="h-4 w-4" />;
      case 'model':
        return <Package className="h-4 w-4" />;
      default:
        return <Star className="h-4 w-4" />;
    }
  };

  const getTypeLabel = (type: string) => {
    return type.charAt(0).toUpperCase() + type.slice(1);
  };

  const handleVariationSelect = (type: string, variationId: string) => {
    const variation = variations.find(v => v.id.toString() === variationId);
    if (variation) {
      setSelectedVariations(prev => ({
        ...prev,
        [type]: variation
      }));
    }
  };

  const allVariationTypesSelected = () => {
    const requiredTypes = Object.keys(variationsByType);
    return requiredTypes.every(type => selectedVariations[type]);
  };

  const getSelectedVariation = () => {
    // For simplicity, return the first selected variation
    // In a real app, you might combine variations differently
    return Object.values(selectedVariations)[0] || null;
  };

  const calculateTotalPrice = () => {
    let basePrice = parseFloat(product.price);
    let totalPrice = basePrice;
    
    // Apply variation price overrides
    Object.values(selectedVariations).forEach(variation => {
      if (variation.price) {
        totalPrice = parseFloat(variation.price);
      }
    });
    
    return totalPrice * quantity;
  };

  const getStockLimit = () => {
    const selectedVariation = getSelectedVariation();
    return selectedVariation ? selectedVariation.stockQuantity : 999;
  };

  const handleQuantityChange = (newQuantity: number) => {
    const stockLimit = getStockLimit();
    if (newQuantity >= 1 && newQuantity <= stockLimit) {
      setQuantity(newQuantity);
    }
  };

  const handleAddToCart = async () => {
    if (!allVariationTypesSelected()) {
      toast({
        title: "Please select all options",
        description: "Please select all product options before adding to cart.",
        variant: "destructive",
      });
      return;
    }

    setIsAdding(true);
    try {
      const selectedVariation = getSelectedVariation();
      await addToCart(product.id, quantity, selectedVariation?.id);

      toast({
        title: "Added to cart",
        description: `${product.name} has been added to your enquiry cart.`,
      });

      onClose();
      onAddToCart?.();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add item to cart. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsAdding(false);
    }
  };

  const handleSubmitEnquiry = () => {
    if (!allVariationTypesSelected()) {
      toast({
        title: "Please select all options",
        description: "Please select all product options before submitting enquiry.",
        variant: "destructive",
      });
      return;
    }

    const selectedVariation = getSelectedVariation();
    const variationPrice = selectedVariation?.price || product.price;

    const enquiryData = {
      productId: product.id,
      productName: product.name,
      productDescription: product.description,
      productImageUrl: product.imageUrl,
      quantity: quantity,
      price: variationPrice,
      // Variation details
      variationId: selectedVariation?.id,
      variationName: selectedVariation?.name,
      variationType: selectedVariation?.type,
      variationValue: selectedVariation?.value,
      // Carton information
      unitsPerCarton: (product as any).unitsPerCarton || 1,
      unitType: (product as any).unitType || 'pieces',
      pricePerUnit: (parseFloat(variationPrice) / ((product as any).unitsPerCarton || 1)).toFixed(2),
    };

    onClose();
    onSubmitEnquiry?.(enquiryData);
  };

  if (isLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Loading options...</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 p-4">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
              <div className="h-10 bg-gray-200 rounded"></div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const handleModalClose = (open: boolean) => {
    if (!open) {
      onClose();
    }
  };

  const handleCancel = (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    onClose();
  };

  if (!isOpen) return null;

  return createPortal(
    <Dialog open={isOpen} onOpenChange={handleModalClose}>
      <DialogContent className="max-w-lg max-h-[90vh] overflow-y-auto">
        <div onClick={(e) => e.stopPropagation()}>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {mode === 'cart' ? (
                <ShoppingCart className="h-5 w-5" />
              ) : (
                <MessageSquare className="h-5 w-5" />
              )}
              {isEditMode
                ? `Edit Options - ${product.name}`
                : mode === 'cart'
                  ? `Select Options for Cart - ${product.name}`
                  : `Select Options for Enquiry - ${product.name}`
              }
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6 p-1">
          {/* Product Info */}
          <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-lg">
            <img
              src={product.imageUrl || "/placeholder.svg"}
              alt={product.name}
              className="h-16 w-16 rounded-md object-cover"
            />
            <div className="flex-1">
              <h3 className="font-medium text-gray-900">{product.name}</h3>
              <p className="text-sm text-gray-600 line-clamp-2">{product.description}</p>
              <div className="mt-2 space-y-1">
                <p className="text-lg font-bold text-forest">
                  Starting at ₹{parseFloat(product.price).toFixed(2)}
                </p>
                <p className="text-xs text-gray-600">
                  per carton ({(product as any).unitsPerCarton || 1} {(product as any).unitType || 'pieces'}) •
                  ₹{(parseFloat(product.price) / ((product as any).unitsPerCarton || 1)).toFixed(2)} per {((product as any).unitType || 'piece').slice(0, -1)}
                </p>
              </div>
            </div>
          </div>

          {/* Variation Selectors */}
          {Object.entries(variationsByType).map(([type, typeVariations]) => (
            <div key={type} className="space-y-3">
              <label className="flex items-center gap-2 text-sm font-medium text-gray-900">
                {getTypeIcon(type)}
                Select {getTypeLabel(type)}
                <span className="text-red-500">*</span>
              </label>
              
              <Select
                value={selectedVariations[type]?.id.toString() || ""}
                onValueChange={(value) => handleVariationSelect(type, value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder={`Choose ${type}...`} />
                </SelectTrigger>
                <SelectContent>
                  {typeVariations
                    .filter(variation => variation.isActive)
                    .map((variation) => (
                      <SelectItem 
                        key={variation.id} 
                        value={variation.id.toString()}
                        disabled={variation.stockQuantity === 0}
                      >
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{variation.name}</span>
                            <span className="text-sm text-gray-500">({variation.value})</span>
                          </div>
                          <div className="flex items-center gap-2 ml-4">
                            {variation.price && (
                              <span className="text-sm font-medium text-forest">
                                ₹{parseFloat(variation.price).toFixed(2)}
                              </span>
                            )}
                            <Badge 
                              variant={variation.stockQuantity > 0 ? "secondary" : "destructive"}
                              className="text-xs"
                            >
                              {variation.stockQuantity > 0 ? `${variation.stockQuantity} left` : "Out of stock"}
                            </Badge>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                </SelectContent>
              </Select>
            </div>
          ))}

          {/* Quantity Selector - Show when all variations selected */}
          {allVariationTypesSelected() && (
            <div className="space-y-3">
              <label className="text-sm font-medium text-gray-900">Quantity</label>
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handleQuantityChange(quantity - 1)}
                  disabled={quantity <= 1}
                  className="h-10 w-10 rounded-full"
                >
                  <Minus className="h-4 w-4" />
                </Button>
                
                <div className="flex-1 text-center">
                  <div className="text-lg font-semibold bg-gray-50 rounded-lg py-2 px-4 min-w-[80px]">
                    {quantity}
                  </div>
                </div>
                
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handleQuantityChange(quantity + 1)}
                  disabled={quantity >= getStockLimit()}
                  className="h-10 w-10 rounded-full"
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              
              {quantity >= getStockLimit() && (
                <p className="text-sm text-amber-600 text-center">
                  Maximum available quantity: {getStockLimit()}
                </p>
              )}
            </div>
          )}

          {/* Price Summary */}
          {allVariationTypesSelected() && (
            <div className="bg-forest/5 border border-forest/20 rounded-lg p-4">
              <div className="space-y-2">
                <h4 className="font-medium text-gray-900">Selected Options:</h4>
                {Object.entries(selectedVariations).map(([type, variation]) => (
                  <div key={type} className="flex justify-between text-sm">
                    <span className="capitalize">{type}: {variation.name}</span>
                    {variation.price && (
                      <span className="font-medium text-forest">
                        ₹{parseFloat(variation.price).toFixed(2)}
                      </span>
                    )}
                  </div>
                ))}
                <div className="border-t pt-2 mt-2">
                  <div className="flex justify-between items-center">
                    <span className="font-medium">Total ({quantity} items):</span>
                    <span className="text-lg font-bold text-forest">
                      ₹{calculateTotalPrice().toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Warning if not all selected */}
          {!allVariationTypesSelected() && (
            <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-amber-800">Selection Required</p>
                  <p className="text-xs text-amber-700 mt-1">
                    Please select all product options above to continue.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="flex-1"
            >
              Cancel
            </Button>
            {mode === 'cart' ? (
              <Button
                onClick={handleAddToCart}
                disabled={!allVariationTypesSelected() || isAdding}
                className="flex-1 bg-forest hover:bg-forest/90"
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                {isAdding ? "Adding..." : "Add to Cart"}
              </Button>
            ) : (
              <Button
                onClick={handleSubmitEnquiry}
                disabled={!allVariationTypesSelected()}
                className="flex-1 bg-sage hover:bg-sage/90"
              >
                <MessageSquare className="h-4 w-4 mr-2" />
                {isEditMode ? 'Save' : 'Submit Enquiry'}
              </Button>
            )}
          </div>
        </div>
        </div>
      </DialogContent>
    </Dialog>,
    document.body
  );
}

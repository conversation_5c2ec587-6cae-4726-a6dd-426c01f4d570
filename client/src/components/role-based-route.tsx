import { ReactNode } from "react";
import { useLocation } from "wouter";
import { useAuth } from "./auth-context";
import { Card, CardContent } from "./ui/card";
import { Loader2, Lock, AlertTriangle } from "lucide-react";
import { But<PERSON> } from "./ui/button";

interface RoleBasedRouteProps {
  children: ReactNode;
  allowedRoles?: string[];
  requireAuth?: boolean;
  fallbackPath?: string;
}

export function RoleBasedRoute({ 
  children, 
  allowedRoles = [], 
  requireAuth = true,
  fallbackPath = "/login"
}: RoleBasedRouteProps) {
  const { isAuthenticated, isLoading, user, hasRole } = useAuth();
  const [, setLocation] = useLocation();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-96">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-forest mb-4" />
            <p className="text-gray-600">Checking permissions...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check if authentication is required
  if (requireAuth && !isAuthenticated) {
    setLocation(fallbackPath);
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-96">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <Lock className="h-8 w-8 text-gray-400 mb-4" />
            <p className="text-gray-600">Redirecting to login...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check role-based access
  if (allowedRoles.length > 0 && user) {
    const hasRequiredRole = allowedRoles.some(role => hasRole(role));
    
    if (!hasRequiredRole) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <Card className="w-96">
            <CardContent className="flex flex-col items-center justify-center py-8">
              <AlertTriangle className="h-8 w-8 text-red-500 mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Access Denied</h3>
              <p className="text-gray-600 text-center mb-4">
                You don't have permission to access this page.
              </p>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => setLocation("/")}
                >
                  Go Home
                </Button>
                <Button
                  onClick={() => window.history.back()}
                >
                  Go Back
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }
  }

  return <>{children}</>;
}

// Convenience components for common use cases
export function AdminRoute({ children }: { children: ReactNode }) {
  return (
    <RoleBasedRoute allowedRoles={["admin"]} fallbackPath="/admin/login">
      {children}
    </RoleBasedRoute>
  );
}

export function UserRoute({ children }: { children: ReactNode }) {
  return (
    <RoleBasedRoute allowedRoles={["user", "admin"]}>
      {children}
    </RoleBasedRoute>
  );
}

export function PublicRoute({ children }: { children: ReactNode }) {
  return (
    <RoleBasedRoute requireAuth={false}>
      {children}
    </RoleBasedRoute>
  );
}

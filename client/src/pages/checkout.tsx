import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { z } from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useCart } from "@/components/cart-context";
import { useAuth } from "@/components/auth-context";
import { useToast } from "@/hooks/use-toast";
import { apiRequest } from "@/lib/queryClient";
import { useLocation } from "wouter";
import { ShoppingCart, MessageSquare, Truck, User, Plus, Minus, Edit, Package, Trash2 } from "lucide-react";
import { ProductVariationModal } from "@/components/product-variation-modal";

const enquirySchema = z.object({
  customerName: z.string().min(2, "Name must be at least 2 characters"),
  customerEmail: z.string().email("Please enter a valid email address"),
  customerPhone: z.string().min(10, "Please enter a valid phone number"),
  deliveryLocation: z.string().min(10, "Please provide complete delivery address including street, area, city, state, and pincode"),
  specialRequests: z.string().optional(),
});

type EnquiryFormData = z.infer<typeof enquirySchema>;

interface EnquiryItem {
  productId: number;
  productName: string;
  productDescription?: string;
  productImageUrl?: string;
  imageUrl?: string; // Alternative field name for compatibility
  quantity: number;
  price: string;
  variationId?: number;
  variationName?: string;
  variationType?: string;
  variationValue?: string;
  unitsPerCarton?: number;
  unitType?: string;
  pricePerUnit?: string;
  product?: any; // For compatibility with cart items
}

export default function Checkout() {
  const { items: cartItems, total: cartTotal, clearCart, updateQuantity: updateCartQuantity, removeFromCart } = useCart();
  const { user } = useAuth();
  const { toast } = useToast();
  const [, setLocation] = useLocation();
  const [isProcessing, setIsProcessing] = useState(false);
  const [directEnquiryItems, setDirectEnquiryItems] = useState<EnquiryItem[]>([]);
  const [editingItem, setEditingItem] = useState<{ index: number; item: any } | null>(null);
  const [showVariationModal, setShowVariationModal] = useState(false);

  // Load direct enquiry data from sessionStorage
  useEffect(() => {
    const directEnquiryData = sessionStorage.getItem('directEnquiryData');
    if (directEnquiryData) {
      try {
        const parsedData = JSON.parse(directEnquiryData);
        setDirectEnquiryItems(parsedData);
        // Clear the session storage after loading
        sessionStorage.removeItem('directEnquiryData');
      } catch (error) {
        console.error('Failed to parse enquiry data:', error);
      }
    }
  }, []);

  // Combine cart items and direct enquiry items
  const allItems = directEnquiryItems.length > 0 ? directEnquiryItems : cartItems;
  const isDirectEnquiry = directEnquiryItems.length > 0;
  const total = isDirectEnquiry
    ? directEnquiryItems.reduce((sum, item) => sum + (parseFloat(item.price) * item.quantity), 0)
    : cartTotal;

  // Functions for editing items
  const updateQuantity = async (index: number, newQuantity: number) => {
    if (newQuantity >= 1) {
      if (isDirectEnquiry) {
        setDirectEnquiryItems(prev => prev.map((item, i) =>
          i === index ? { ...item, quantity: newQuantity } : item
        ));
      } else {
        // For cart items, use the cart context to update quantities
        const item = cartItems[index];
        if (item) {
          try {
            await updateCartQuantity(item.productId, newQuantity, item.variationId || undefined);
            toast({
              title: "Success",
              description: "Quantity updated successfully",
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Failed to update quantity",
              variant: "destructive",
            });
          }
        }
      }
    }
  };

  const removeItem = async (index: number) => {
    if (isDirectEnquiry) {
      setDirectEnquiryItems(prev => prev.filter((_, i) => i !== index));
      toast({
        title: "Success",
        description: "Item removed from enquiry",
      });
    } else {
      // For cart items, use the cart context to remove items
      const item = cartItems[index];
      if (item) {
        try {
          await removeFromCart(item.productId, item.variationId || undefined);
          toast({
            title: "Success",
            description: "Item removed from cart",
          });
        } catch (error) {
          toast({
            title: "Error",
            description: "Failed to remove item",
            variant: "destructive",
          });
        }
      }
    }
  };

  const handleEditVariation = async (index: number, item: any) => {
    // Set the item being edited and open the variation modal
    setEditingItem({ index, item });
    setShowVariationModal(true);
  };

  const handleVariationUpdate = (updatedVariationData: any) => {
    if (editingItem && isDirectEnquiry) {
      // Update the direct enquiry item with new variation data
      setDirectEnquiryItems(prev => prev.map((item, i) =>
        i === editingItem.index ? {
          ...item,
          price: updatedVariationData.price,
          variationId: updatedVariationData.variationId,
          variationName: updatedVariationData.variationName,
          variationType: updatedVariationData.variationType,
          variationValue: updatedVariationData.variationValue,
          pricePerUnit: updatedVariationData.pricePerUnit,
        } : item
      ));

      toast({
        title: "Variation updated",
        description: "Product variation has been updated successfully.",
      });
    } else {
      toast({
        title: "Note",
        description: "Cart item variations cannot be edited here. Please update from the cart page.",
      });
    }

    setShowVariationModal(false);
    setEditingItem(null);
  };

  const form = useForm<EnquiryFormData>({
    resolver: zodResolver(enquirySchema),
    defaultValues: {
      customerName: user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : "",
      customerEmail: user?.email || "",
      customerPhone: "",
      deliveryLocation: "",
      specialRequests: "",
    },
  });

  const createEnquiryMutation = useMutation({
    mutationFn: async (enquiryData: any) => {
      const response = await apiRequest("POST", "/api/orders", enquiryData);
      return response.json();
    },
    onSuccess: (order) => {
      toast({
        title: "Order enquiry submitted successfully!",
        description: `Your enquiry #${order.id} has been submitted. We'll contact you shortly to confirm details and pricing.`,
      });
      if (!isDirectEnquiry) {
        clearCart();
      } else {
        setDirectEnquiryItems([]);
      }
      setLocation("/");
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to submit enquiry. Please try again.",
        variant: "destructive",
      });
    },
  });

  // Redirect if no items
  if (allItems.length === 0) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <ShoppingCart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Your enquiry cart is empty</h1>
          <p className="text-gray-600 mb-6">Add some products to your enquiry cart before proceeding.</p>
          <Button onClick={() => setLocation("/products")}>
            Continue Shopping
          </Button>
        </div>
      </div>
    );
  }

  const subtotal = total;
  const orderTotal = subtotal; // No tax or shipping for enquiry system

  const onSubmit = async (data: EnquiryFormData) => {
    setIsProcessing(true);
    try {
      const enquiryData = {
        ...data,
        items: allItems.map((item: any) => {
          if (isDirectEnquiry) {
            // Direct enquiry items already have the correct structure
            return {
              productId: item.productId,
              productName: item.productName,
              quantity: item.quantity,
              price: item.price,
              variationId: item.variationId,
              variationName: item.variationName,
              variationType: item.variationType,
              variationValue: item.variationValue,
              unitsPerCarton: item.unitsPerCarton,
              unitType: item.unitType,
              pricePerUnit: item.pricePerUnit,
            };
          } else {
            // Cart items need to be transformed
            return {
              productId: item.productId,
              productName: item.product.name,
              quantity: item.quantity,
              price: (item as any).variation?.price || item.product.price,
              // Variation details
              variationId: (item as any).variationId || undefined,
              variationName: (item as any).variation?.name || undefined,
              variationType: (item as any).variation?.type || undefined,
              variationValue: (item as any).variation?.value || undefined,
              // Carton information (use variation-specific if available, fallback to product defaults)
              unitsPerCarton: (item as any).variation?.unitsPerCarton || (item.product as any).unitsPerCarton || undefined,
              unitType: (item as any).variation?.unitType || (item.product as any).unitType || undefined,
              pricePerUnit: ((item as any).variation?.unitsPerCarton || (item.product as any).unitsPerCarton)
                ? (parseFloat((item as any).variation?.price || item.product.price) / ((item as any).variation?.unitsPerCarton || (item.product as any).unitsPerCarton)).toFixed(2)
                : undefined,
            };
          }
        }),
        subtotal: subtotal.toFixed(2),
        tax: "0.00", // Add missing tax field
        total: orderTotal.toFixed(2),
        isGuest: !user, // Track if this is a guest enquiry
        userId: user?.id || null, // Include user ID if logged in
        sessionId: !user ? `guest_${Date.now()}_${Math.random().toString(36).substring(2, 11)}` : null, // Generate session ID for guests
      };

      await createEnquiryMutation.mutateAsync(enquiryData);
    } catch (error) {
      console.error("Enquiry submission failed:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Order Enquiry</h1>
          <p className="text-gray-600 mt-2">
            {user ? `Hello ${user.firstName || user.username}! ` : ""}
            Complete your enquiry details below - no payment required
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Order Form */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MessageSquare className="h-5 w-5" />
                  <span>{user ? "Your Details" : "Contact Information"}</span>
                </CardTitle>
                {user && (
                  <p className="text-sm text-gray-600">
                    We've pre-filled your details. You can modify them if needed.
                  </p>
                )}
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <div className="grid md:grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name="customerName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Full Name *</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter your full name" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="customerEmail"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Email Address *</FormLabel>
                            <FormControl>
                              <Input type="email" placeholder="<EMAIL>" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="customerPhone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Phone Number *</FormLabel>
                          <FormControl>
                            <Input type="tel" placeholder="+91 98765 43210" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <Separator />

                    <div>
                      <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
                        <Truck className="h-5 w-5" />
                        <span>Delivery Location</span>
                      </h3>

                      <FormField
                        control={form.control}
                        name="deliveryLocation"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Complete Delivery Address *</FormLabel>
                            <FormControl>
                              <Textarea
                                placeholder={`Enter your complete delivery address including:
• Street address and house/flat number
• Area/Locality
• Landmarks (if any)
• City, State
• Pincode

Example:
123, Green Park Apartments
Sector 15, Koramangala
Near Metro Station
Bangalore, Karnataka - 560034`}
                                className="min-h-[120px] resize-none"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                            <p className="text-sm text-gray-600 mt-2">
                              Please provide complete address details to ensure accurate delivery estimation.
                            </p>
                          </FormItem>
                        )}
                      />
                    </div>

                    <Separator />

                    <FormField
                      control={form.control}
                      name="specialRequests"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Special Requests or Notes (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Any special requirements, preferred delivery time, or additional notes..."
                              className="min-h-[80px] resize-none"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                          <p className="text-sm text-gray-600 mt-1">
                            Let us know if you have any specific requirements or questions.
                          </p>
                        </FormItem>
                      )}
                    />

                    <Button
                      type="submit"
                      className="w-full bg-forest hover:bg-forest/90 text-white py-3 text-lg"
                      disabled={isProcessing}
                    >
                      {isProcessing ? "Submitting Enquiry..." : "Submit Order Enquiry"}
                    </Button>

                    <div className="text-center text-sm text-gray-600 mt-4">
                      <p>
                        <strong>No payment required!</strong> We'll contact you to confirm details and provide final pricing.
                      </p>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Enquiry Summary ({allItems.length} items)</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {allItems.map((item: any, index: number) => (
                  <div key={`${item.productId}-${index}`} className="border rounded-lg p-4 space-y-4">
                    {/* Product Header */}
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-semibold text-lg text-gray-900 mb-2">
                          {isDirectEnquiry ? item.productName : item.product.name}
                        </h4>
                        <p className="text-sm text-gray-500 mb-3">Product ID: {item.productId}</p>

                        {/* Product Description */}
                        {(isDirectEnquiry ? item.productDescription : item.product?.description) && (
                          <div className="mb-3">
                            <p className="text-sm text-gray-700 leading-relaxed">
                              {isDirectEnquiry ? item.productDescription : item.product.description}
                            </p>
                          </div>
                        )}
                      </div>
                      <div className="text-right ml-4">
                        <p className="font-bold text-forest text-xl">
                          ₹{(parseFloat(item.price || item.product?.price) * item.quantity).toFixed(2)}
                        </p>
                        <p className="text-sm text-gray-500">
                          ₹{parseFloat(item.price || item.product?.price).toFixed(2)} × {item.quantity}
                        </p>
                      </div>
                    </div>

                    {/* Variation Details */}
                    {(item.variationId || (item as any).variation) && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <h5 className="font-semibold text-blue-900 flex items-center gap-2">
                            🎯 Selected Options
                          </h5>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditVariation(index, item)}
                            className="text-xs text-blue-700 hover:text-blue-800 hover:bg-blue-100"
                          >
                            <Edit className="h-3 w-3 mr-1" />
                            Edit Options
                          </Button>
                        </div>
                        <div className="space-y-2 text-sm">
                          <div className="flex items-center">
                            <span className="text-blue-700 font-medium w-16">Type:</span>
                            <span className="capitalize text-gray-800">
                              {item.variationType || (item as any).variation?.type || 'N/A'}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-blue-700 font-medium w-16">Option:</span>
                            <span className="text-gray-800">
                              {item.variationName || (item as any).variation?.name || 'N/A'}
                            </span>
                          </div>
                          <div className="flex items-center">
                            <span className="text-blue-700 font-medium w-16">Value:</span>
                            <span className="text-gray-800 font-medium">
                              {item.variationValue || (item as any).variation?.value || 'N/A'}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Carton Information */}
                    {(() => {
                      // Get carton info from variation first, then fallback to product
                      const variation = (item as any).variation;
                      const product = item.product as any;
                      const unitsPerCarton = variation?.unitsPerCarton || item.unitsPerCarton || product?.unitsPerCarton;
                      const unitType = variation?.unitType || item.unitType || product?.unitType;
                      const cartonWeight = variation?.cartonWeight || product?.cartonWeight;
                      const cartonDimensions = variation?.cartonDimensions || product?.cartonDimensions;

                      return unitsPerCarton && (
                        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                          <h5 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                            📦 Carton Information
                            {variation?.unitsPerCarton && (
                              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                Variation-specific
                              </span>
                            )}
                          </h5>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                            <div className="flex items-center">
                              <span className="text-gray-700 font-medium w-32">Units per carton:</span>
                              <span className="text-gray-900 font-semibold">
                                {unitsPerCarton} {unitType}
                              </span>
                            </div>
                            <div className="flex items-center">
                              <span className="text-gray-700 font-medium w-32">Price per unit:</span>
                              <span className="text-forest font-bold">
                                ₹{item.pricePerUnit || (parseFloat(item.price || item.product?.price) / unitsPerCarton).toFixed(2)}
                              </span>
                            </div>
                            {cartonWeight && (
                              <div className="flex items-center">
                                <span className="text-gray-700 font-medium w-32">Carton weight:</span>
                                <span className="text-gray-900">{cartonWeight} kg</span>
                              </div>
                            )}
                            {cartonDimensions && (
                              <div className="flex items-center">
                                <span className="text-gray-700 font-medium w-32">Dimensions:</span>
                                <span className="text-gray-900">{cartonDimensions}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })()}

                    {/* Quantity Controls and Actions */}
                    <div className="flex items-center justify-between bg-gray-50 rounded-lg p-4">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-3">
                          <span className="text-sm font-medium text-gray-700">Quantity:</span>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => updateQuantity(index, item.quantity - 1)}
                              disabled={item.quantity <= 1}
                              className="h-9 w-9 hover:bg-red-50 hover:border-red-300"
                            >
                              <Minus className="h-4 w-4" />
                            </Button>
                            <span className="w-12 text-center font-semibold text-lg">{item.quantity}</span>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => updateQuantity(index, item.quantity + 1)}
                              className="h-9 w-9 hover:bg-green-50 hover:border-green-300"
                            >
                              <Plus className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        {/* Delete Button */}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => removeItem(index)}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
                        >
                          <Trash2 className="h-4 w-4 mr-1" />
                          Remove
                        </Button>
                      </div>

                      <div className="text-right">
                        <p className="text-sm text-gray-600">
                          ₹{parseFloat(item.price || item.product?.price).toFixed(2)} per carton
                        </p>
                        <p className="font-semibold text-forest text-lg">
                          Total: ₹{(parseFloat(item.price || item.product?.price) * item.quantity).toFixed(2)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between text-lg font-semibold">
                    <span>Estimated Total:</span>
                    <span className="text-forest">₹{total.toFixed(2)}</span>
                  </div>
                  <p className="text-sm text-gray-600">
                    * This is an estimated total. Final pricing will be confirmed when we contact you.
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <h3 className="font-semibold">What Happens Next?</h3>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <MessageSquare className="h-4 w-4 text-green-600" />
                      <span>We'll contact you within 24 hours</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <Truck className="h-4 w-4 text-blue-600" />
                      <span>Confirm delivery details and final pricing</span>
                    </div>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <User className="h-4 w-4 text-purple-600" />
                      <span>No payment required until confirmation</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Variation Edit Modal */}
      {showVariationModal && editingItem && (
        <ProductVariationModal
          product={editingItem.item.product || {
            id: editingItem.item.productId,
            name: editingItem.item.productName,
            description: editingItem.item.productDescription || "Product description",
            price: editingItem.item.price,
            imageUrl: editingItem.item.productImageUrl || editingItem.item.imageUrl || "/placeholder.svg",
            hasVariations: true,
            // Use variation-specific carton info if available, fallback to product defaults
            unitsPerCarton: (editingItem.item as any).variation?.unitsPerCarton || editingItem.item.unitsPerCarton || (editingItem.item.product as any)?.unitsPerCarton,
            unitType: (editingItem.item as any).variation?.unitType || editingItem.item.unitType || (editingItem.item.product as any)?.unitType,
          }}
          isOpen={showVariationModal}
          onClose={() => {
            setShowVariationModal(false);
            setEditingItem(null);
          }}
          mode="enquiry"
          isEditMode={true}
          initialVariation={{
            id: editingItem.item.variationId,
            name: editingItem.item.variationName,
            type: editingItem.item.variationType,
            value: editingItem.item.variationValue,
            price: editingItem.item.price,
          }}
          initialQuantity={editingItem.item.quantity}
          onSubmitEnquiry={handleVariationUpdate}
        />
      )}
    </div>
  );
}

import { useState, useEffect } from "react";
import { useLocation, Link } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Leaf, Lock, User, Shield, Smartphone } from "lucide-react";
import { useAuth } from "@/components/auth-context";
import { useToast } from "@/hooks/use-toast";

export default function Login() {
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [twoFactorCode, setTwoFactorCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [loginType, setLoginType] = useState<"user" | "admin">("user");
  const [requiresTwoFactor, setRequiresTwoFactor] = useState(false);
  const [pendingUser, setPendingUser] = useState<any>(null);
  const [csrfToken, setCsrfToken] = useState("");
  const [csrfLoading, setCsrfLoading] = useState(true);
  const [, setLocation] = useLocation();
  const { login, isAuthenticated, isLoading: authLoading, user } = useAuth();
  const { toast } = useToast();

  // Get CSRF token on component mount
  useEffect(() => {
    setCsrfLoading(true);
    fetch('/api/auth/csrf-token', { credentials: 'include' })
      .then(res => res.json())
      .then(data => {
        setCsrfToken(data.csrfToken);
        setCsrfLoading(false);
      })
      .catch(error => {
        console.error('Failed to get CSRF token:', error);
        setCsrfLoading(false);
      });
  }, []);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !authLoading && user) {
      // Redirect based on user role
      if (user.role === "admin") {
        setLocation("/admin");
      } else {
        setLocation("/");
      }
    }
  }, [isAuthenticated, authLoading, user, setLocation]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      // Ensure we have a CSRF token
      if (!csrfToken) {
        const csrfResponse = await fetch('/api/auth/csrf-token', {
          credentials: 'include',
        });
        if (csrfResponse.ok) {
          const { csrfToken: newToken } = await csrfResponse.json();
          setCsrfToken(newToken);
        } else {
          throw new Error('Failed to get security token');
        }
      }

      // First attempt login with username/password
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({
          username,
          password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Login failed');
      }

      // Check if 2FA is required
      if (data.requiresTwoFactor) {
        setRequiresTwoFactor(true);
        setPendingUser(data.user || { username });
        toast({
          title: "2FA Required",
          description: "Please enter your two-factor authentication code.",
        });
      } else {
        // Login complete - redirect based on user role
        toast({
          title: "Login successful",
          description: `Welcome back${loginType === "admin" ? " to admin panel" : ""}!`,
        });

        // Redirect based on user role
        if (loginType === "admin") {
          setLocation("/admin");
        } else {
          setLocation("/");
        }
      }
    } catch (err: any) {
      setError(err.message || "Login failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handle2FASubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      // Ensure we have a CSRF token
      if (!csrfToken) {
        const csrfResponse = await fetch('/api/auth/csrf-token', {
          credentials: 'include',
        });
        if (csrfResponse.ok) {
          const { csrfToken: newToken } = await csrfResponse.json();
          setCsrfToken(newToken);
        } else {
          throw new Error('Failed to get security token');
        }
      }

      const response = await fetch('/api/auth/verify-2fa', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken,
        },
        credentials: 'include',
        body: JSON.stringify({
          token: twoFactorCode,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || '2FA verification failed');
      }

      // 2FA verification successful
      toast({
        title: "Login successful",
        description: `Welcome back${loginType === "admin" ? " to admin panel" : ""}!`,
      });

      // Redirect based on user role
      if (loginType === "admin") {
        setLocation("/admin");
      } else {
        setLocation("/");
      }

    } catch (err: any) {
      setError(err.message || "2FA verification failed. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-96">
          <CardContent className="flex flex-col items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-forest mb-4"></div>
            <p className="text-gray-600">Checking authentication...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="flex justify-center">
            <Leaf className="h-12 w-12 text-forest" />
          </div>
          <h2 className="mt-6 text-3xl font-bold text-gray-900">Sign in to your account</h2>
          <p className="mt-2 text-sm text-gray-600">
            New user?{" "}
            <Link href="/register">
              <a className="font-medium text-forest hover:text-forest-dark">
                Create an account
              </a>
            </Link>
          </p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="text-center">
              {requiresTwoFactor ? "Two-Factor Authentication" : "Choose Login Type"}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {requiresTwoFactor ? (
              // 2FA Verification Form
              <div className="space-y-6">
                <div className="text-center">
                  <div className="mx-auto mb-4 w-12 h-12 bg-forest rounded-full flex items-center justify-center">
                    <Smartphone className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Enter Authentication Code</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    Please enter the 6-digit code from your authenticator app
                  </p>
                  {pendingUser && (
                    <p className="text-xs text-gray-500">
                      Logging in as: <strong>{pendingUser.username}</strong>
                    </p>
                  )}
                </div>

                <form onSubmit={handle2FASubmit} className="space-y-4">
                  {error && (
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="twoFactorCode">Authentication Code</Label>
                    <div className="relative">
                      <Smartphone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="twoFactorCode"
                        type="text"
                        value={twoFactorCode}
                        onChange={(e) => setTwoFactorCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                        placeholder="000000"
                        className="pl-10 text-center text-lg tracking-widest"
                        maxLength={6}
                        required
                        disabled={isLoading}
                        autoComplete="one-time-code"
                      />
                    </div>
                    <p className="text-xs text-gray-500 text-center">
                      Enter the 6-digit code from Google Authenticator or your authenticator app
                    </p>
                  </div>

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isLoading || csrfLoading || twoFactorCode.length !== 6}
                  >
                    {isLoading ? "Verifying..." : csrfLoading ? "Loading..." : "Verify & Sign In"}
                  </Button>

                  <div className="text-center">
                    <Button
                      type="button"
                      variant="link"
                      onClick={() => {
                        setRequiresTwoFactor(false);
                        setPendingUser(null);
                        setTwoFactorCode("");
                        setError("");
                      }}
                      className="text-sm text-gray-600 hover:text-forest"
                    >
                      ← Back to login
                    </Button>
                  </div>
                </form>
              </div>
            ) : (
              // Regular Login Tabs
              <Tabs value={loginType} onValueChange={(value) => setLoginType(value as "user" | "admin")} className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="user" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  User Login
                </TabsTrigger>
                <TabsTrigger value="admin" className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Admin Login
                </TabsTrigger>
              </TabsList>

              <TabsContent value="user" className="mt-6">
                <div className="text-center mb-4">
                  <h3 className="text-lg font-semibold">User Login</h3>
                  <p className="text-sm text-gray-600">Access your account and shop</p>
                </div>
                <form onSubmit={handleSubmit} className="space-y-4">
                  {error && (
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="username">Username</Label>
                    <div className="relative">
                      <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="username"
                        type="text"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        placeholder="Enter your username"
                        className="pl-10"
                        required
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="password">Password</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="password"
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Enter your password"
                        className="pl-10"
                        required
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isLoading || csrfLoading}
                  >
                    {isLoading ? "Signing in..." : csrfLoading ? "Loading..." : "Sign In as User"}
                  </Button>
                </form>
              </TabsContent>

              <TabsContent value="admin" className="mt-6">
                <div className="text-center mb-4">
                  <h3 className="text-lg font-semibold">Admin Login</h3>
                  <p className="text-sm text-gray-600">Access admin panel and management tools</p>
                </div>

                <form onSubmit={handleSubmit} className="space-y-4">
                  {error && (
                    <Alert variant="destructive">
                      <AlertDescription>{error}</AlertDescription>
                    </Alert>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="admin-username">Username</Label>
                    <div className="relative">
                      <Shield className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="admin-username"
                        type="text"
                        value={username}
                        onChange={(e) => setUsername(e.target.value)}
                        placeholder="Enter admin username"
                        className="pl-10"
                        required
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="admin-password">Password</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="admin-password"
                        type="password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        placeholder="Enter admin password"
                        className="pl-10"
                        required
                        disabled={isLoading}
                      />
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full"
                    disabled={isLoading || csrfLoading}
                  >
                    {isLoading ? "Signing in..." : csrfLoading ? "Loading..." : "Sign In as Admin"}
                  </Button>

                  <div className="text-center text-xs text-gray-500 mt-2">
                    <p>Default: admin / admin123</p>
                  </div>
                </form>
              </TabsContent>
            </Tabs>
            )}

            {!requiresTwoFactor && (
              <div className="mt-6 text-center">
                <Button
                  variant="link"
                  onClick={() => setLocation("/")}
                  className="text-sm text-gray-600 hover:text-forest"
                >
                  ← Back to website
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

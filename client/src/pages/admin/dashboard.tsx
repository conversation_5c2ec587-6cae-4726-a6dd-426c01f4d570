import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { Users, ShoppingBag, Package, AlertCircle, TrendingUp, Activity } from "lucide-react";
import { AdminLayout } from "@/components/layout/admin-layout";

import type { Order, Contact, Product } from "@shared/schema";

export default function AdminDashboard() {
  const { data: contacts = [] } = useQuery<Contact[]>({
    queryKey: ["/api/admin/contacts"],
  });

  const { data: orders = [] } = useQuery<Order[]>({
    queryKey: ["/api/admin/orders"],
  });

  const { data: products = [] } = useQuery<Product[]>({
    queryKey: ["/api/admin/products"],
  });

  const unseenOrders = orders.filter(order => !order.seen).length;
  const unseenContacts = contacts.filter(contact => !contact.seen).length;

  return (
    <AdminLayout>
      <div className="space-y-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Admin Dashboard</h1>
          <p className="text-gray-600">Welcome to your admin control panel</p>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Orders</CardTitle>
              <ShoppingBag className="h-5 w-5 text-forest" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">{orders.length}</div>
              {unseenOrders > 0 && (
                <div className="flex items-center mt-2">
                  <AlertCircle className="h-4 w-4 text-orange-500 mr-1" />
                  <p className="text-sm text-orange-600 font-medium">
                    {unseenOrders} new order{unseenOrders > 1 ? 's' : ''}
                  </p>
                </div>
              )}
              <p className="text-xs text-gray-500 mt-1">Total customer orders</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Inquiries</CardTitle>
              <Users className="h-5 w-5 text-forest" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">{contacts.length}</div>
              {unseenContacts > 0 && (
                <div className="flex items-center mt-2">
                  <AlertCircle className="h-4 w-4 text-orange-500 mr-1" />
                  <p className="text-sm text-orange-600 font-medium">
                    {unseenContacts} new inquiry{unseenContacts > 1 ? 's' : ''}
                  </p>
                </div>
              )}
              <p className="text-xs text-gray-500 mt-1">Customer contact requests</p>
            </CardContent>
          </Card>

          <Card className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between pb-2">
              <CardTitle className="text-sm font-medium text-gray-600">Products</CardTitle>
              <Package className="h-5 w-5 text-forest" />
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-900">{products.length}</div>
              <div className="flex items-center mt-2">
                <Activity className="h-4 w-4 text-green-500 mr-1" />
                <p className="text-sm text-green-600 font-medium">
                  {products.filter(p => p.inStock).length} in stock
                </p>
              </div>
              <p className="text-xs text-gray-500 mt-1">Total product catalog</p>
            </CardContent>
          </Card>
        </div>


      </div>
    </AdminLayout>
  );
}
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AdminLayout } from "@/components/layout/admin-layout";
import { SecurityDashboard } from "@/components/security-dashboard";
import { TwoFactorSetup } from "@/components/two-factor-setup";
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Settings,
  Smartphone,
  Key,
  Activity,
  Lock
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";

interface AuthStatus {
  authenticated: boolean;
  isAdmin: boolean;
  twoFactorVerified: boolean;
  pendingTwoFactor: boolean;
  csrfToken: string;
}

export default function AdminSecurity() {
  const [showTwoFactorSetup, setShowTwoFactorSetup] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'settings' | 'logs'>('overview');

  // Check authentication status
  const { data: authStatus, refetch } = useQuery<AuthStatus>({
    queryKey: ['/api/auth/status'],
    refetchInterval: 30000,
  });

  if (showTwoFactorSetup) {
    return (
      <TwoFactorSetup
        onSetupComplete={() => {
          setShowTwoFactorSetup(false);
          refetch();
        }}
        onCancel={() => setShowTwoFactorSetup(false)}
      />
    );
  }

  return (
    <AdminLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Security Center</h1>
          <p className="text-gray-600">Manage your admin account security and monitor system activity</p>
        </div>

        {/* Security Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-full ${authStatus?.twoFactorVerified ? 'bg-green-100' : 'bg-orange-100'}`}>
                  <Shield className={`h-6 w-6 ${authStatus?.twoFactorVerified ? 'text-green-600' : 'text-orange-600'}`} />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Two-Factor Authentication</p>
                  <p className={`text-lg font-bold ${authStatus?.twoFactorVerified ? 'text-green-600' : 'text-orange-600'}`}>
                    {authStatus?.twoFactorVerified ? 'Enabled' : 'Not Enabled'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-full bg-green-100">
                  <Lock className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Session Security</p>
                  <p className="text-lg font-bold text-green-600">Active</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-full bg-blue-100">
                  <Activity className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-600">Security Monitoring</p>
                  <p className="text-lg font-bold text-blue-600">Active</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Security Recommendations */}
        {!authStatus?.twoFactorVerified && (
          <Alert className="mb-6 border-orange-200 bg-orange-50">
            <AlertTriangle className="h-4 w-4 text-orange-600" />
            <AlertDescription className="text-orange-800">
              <div className="flex items-center justify-between">
                <span>
                  <strong>Security Recommendation:</strong> Enable two-factor authentication to enhance your account security.
                </span>
                <Button
                  size="sm"
                  onClick={() => setShowTwoFactorSetup(true)}
                  className="bg-orange-600 hover:bg-orange-700 text-white ml-4"
                >
                  Setup 2FA
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Tab Navigation */}
        <div className="border-b border-gray-200 mb-6">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', label: 'Security Overview', icon: Shield },
              { id: 'settings', label: 'Security Settings', icon: Settings },
              { id: 'logs', label: 'Security Logs', icon: Activity },
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-forest text-forest'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <SecurityDashboard onSetup2FA={() => setShowTwoFactorSetup(true)} />
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Smartphone className="h-5 w-5" />
                  Two-Factor Authentication
                </CardTitle>
                <CardDescription>
                  Add an extra layer of security to your admin account
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Shield className={`h-8 w-8 ${authStatus?.twoFactorVerified ? 'text-green-600' : 'text-gray-400'}`} />
                    <div>
                      <p className="font-medium">Authenticator App</p>
                      <p className="text-sm text-gray-500">
                        Use Google Authenticator, Authy, or similar apps
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Badge variant={authStatus?.twoFactorVerified ? "default" : "secondary"}>
                      {authStatus?.twoFactorVerified ? 'Enabled' : 'Disabled'}
                    </Badge>
                    <Button
                      size="sm"
                      onClick={() => setShowTwoFactorSetup(true)}
                      variant={authStatus?.twoFactorVerified ? "outline" : "default"}
                      className={!authStatus?.twoFactorVerified ? "bg-forest hover:bg-forest/90" : ""}
                    >
                      {authStatus?.twoFactorVerified ? 'Manage' : 'Setup'}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Key className="h-5 w-5" />
                  Password Security
                </CardTitle>
                <CardDescription>
                  Manage your password and security preferences
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Change Password</p>
                    <p className="text-sm text-gray-500">
                      Update your admin password regularly for better security
                    </p>
                  </div>
                  <Button variant="outline" size="sm">
                    Change Password
                  </Button>
                </div>
                
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Session Timeout</p>
                    <p className="text-sm text-gray-500">
                      Admin sessions automatically expire after 4 hours
                    </p>
                  </div>
                  <Badge variant="secondary">4 hours</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'logs' && (
          <div className="space-y-6">
            <SecurityDashboard onSetup2FA={() => setShowTwoFactorSetup(true)} />
          </div>
        )}
      </div>
    </AdminLayout>
  );
}

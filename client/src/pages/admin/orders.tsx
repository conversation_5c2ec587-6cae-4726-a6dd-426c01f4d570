import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { AdminLayout } from "@/components/layout/admin-layout";
import { Eye, EyeOff, Trash2, Info, User, Phone, Mail, MapPin, MessageSquare, Package, Calendar } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import type { Order } from "@shared/schema";

export default function AdminOrders() {
  const [filter, setFilter] = useState<"all" | "seen" | "unseen">("all");
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { data: orders = [], isLoading } = useQuery<Order[]>({
    queryKey: ["/api/admin/orders"],
  });

  const markSeenMutation = useMutation({
    mutationFn: async ({ id, seen }: { id: number; seen: boolean }) => {
      const response = await fetch(`/api/admin/orders/${id}/seen`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ seen }),
      });

      if (!response.ok) {
        throw new Error("Failed to update order");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/orders"] });
      toast({
        title: "Order updated",
        description: "Order status has been updated successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update order status.",
        variant: "destructive",
      });
    },
  });

  const updateOrderStatusMutation = useMutation({
    mutationFn: async ({ id, status }: { id: number; status: string }) => {
      const response = await fetch(`/api/admin/orders/${id}/status`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        throw new Error("Failed to update order status");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/orders"] });
      toast({
        title: "Order status updated",
        description: "Order status has been updated successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update order status.",
        variant: "destructive",
      });
    },
  });

  const deleteOrderMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await fetch(`/api/admin/orders/${id}`, {
        method: "DELETE",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Failed to delete order");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/orders"] });
      toast({
        title: "Order deleted",
        description: "Order has been deleted successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to delete order.",
        variant: "destructive",
      });
    },
  });

  const filteredOrders = orders.filter((order) => {
    if (filter === "seen") return order.seen;
    if (filter === "unseen") return !order.seen;
    return true;
  });

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending": return "bg-yellow-100 text-yellow-800";
      case "processing": return "bg-blue-100 text-blue-800";
      case "shipped": return "bg-green-100 text-green-800";
      case "delivered": return "bg-green-100 text-green-800";
      case "cancelled": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  // Available order statuses
  const orderStatuses = [
    { value: "pending", label: "Pending", color: "bg-yellow-100 text-yellow-800" },
    { value: "confirmed", label: "Confirmed", color: "bg-blue-100 text-blue-800" },
    { value: "processing", label: "Processing", color: "bg-purple-100 text-purple-800" },
    { value: "shipped", label: "Shipped", color: "bg-green-100 text-green-800" },
    { value: "delivered", label: "Delivered", color: "bg-green-100 text-green-800" },
    { value: "cancelled", label: "Cancelled", color: "bg-red-100 text-red-800" },
  ];

  // Detailed Order View Component
  const OrderDetailsDialog = ({ order }: { order: Order }) => (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          <Info className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5" />
            <span>Order Details - #{order.id}</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Order Management Section */}
          <Card className="border-forest/20">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-forest">
                <Package className="h-5 w-5" />
                <span>Order Management</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    Order Date
                  </label>
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{formatDate(order.createdAt)}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    Current Status
                  </label>
                  <Badge className={getStatusColor(order.status)}>
                    {order.status}
                  </Badge>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    Update Status
                  </label>
                  <Select
                    value={order.status}
                    onValueChange={(newStatus) => {
                      if (newStatus !== order.status) {
                        updateOrderStatusMutation.mutate({ id: order.id, status: newStatus });
                      }
                    }}
                    disabled={updateOrderStatusMutation.isPending}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      {orderStatuses.map((status) => (
                        <SelectItem key={status.value} value={status.value}>
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${status.color.split(' ')[0]}`} />
                            <span>{status.label}</span>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              {updateOrderStatusMutation.isPending && (
                <div className="mt-3 text-sm text-blue-600 flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span>Updating order status...</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Order Status & Date */}
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">Order ID</span>
                </div>
                <p className="text-sm text-gray-600">#{order.id}</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2 mb-2">
                  <Package className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">Total Value</span>
                </div>
                <p className="text-lg font-semibold text-forest">₹{parseFloat(order.total).toFixed(2)}</p>
              </CardContent>
            </Card>
          </div>

          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>Customer Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">Name:</span>
                  <span>{order.customerName}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">Email:</span>
                  <span>{order.customerEmail}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">Phone:</span>
                  <span>{order.customerPhone || "Not provided"}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Package className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">User Type:</span>
                  <Badge variant={order.isGuest ? "secondary" : "default"}>
                    {order.isGuest ? "Guest" : "Registered User"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Delivery Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MapPin className="h-5 w-5" />
                <span>Delivery Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div>
                  <span className="font-medium">Delivery Address:</span>
                  <p className="text-sm text-gray-600 mt-1 whitespace-pre-wrap">
                    {order.deliveryLocation ||
                     (order.shippingAddress ?
                      `${order.shippingAddress.street}, ${order.shippingAddress.city}, ${order.shippingAddress.state} - ${order.shippingAddress.zipCode}` :
                      "Address not provided")}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Special Requests */}
          {order.specialRequests && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <MessageSquare className="h-5 w-5" />
                  <span>Special Requests</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 whitespace-pre-wrap">
                  {order.specialRequests}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Order Items */}
          <Card>
            <CardHeader>
              <CardTitle>Order Items ({order.items.length} items)</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {order.items.map((item, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-3">
                    {/* Product Header */}
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-lg">{item.productName}</h4>
                        <p className="text-sm text-gray-600">Product ID: {item.productId}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">Qty: {item.quantity}</p>
                        <p className="font-semibold text-forest text-lg">
                          ₹{(parseFloat(item.price) * item.quantity).toFixed(2)}
                        </p>
                      </div>
                    </div>

                    {/* Variation Details */}
                    {(item as any).variationId && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                        <h5 className="font-medium text-blue-900 mb-2 flex items-center gap-2">
                          🎯 Selected Options
                        </h5>
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div>
                            <span className="text-blue-700 font-medium">Type:</span>
                            <span className="ml-2 capitalize">{(item as any).variationType || 'N/A'}</span>
                          </div>
                          <div>
                            <span className="text-blue-700 font-medium">Option:</span>
                            <span className="ml-2">{(item as any).variationName || 'N/A'}</span>
                          </div>
                          <div>
                            <span className="text-blue-700 font-medium">Value:</span>
                            <span className="ml-2">{(item as any).variationValue || 'N/A'}</span>
                          </div>
                          <div>
                            <span className="text-blue-700 font-medium">Variation ID:</span>
                            <span className="ml-2">#{(item as any).variationId}</span>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Carton Information */}
                    {(item as any).unitsPerCarton && (
                      <div className="bg-sage/10 border border-sage/20 rounded-lg p-3">
                        <h5 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
                          📦 Carton Details
                        </h5>
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div>
                            <span className="text-gray-700 font-medium">Units per carton:</span>
                            <span className="ml-2">{(item as any).unitsPerCarton} {(item as any).unitType || 'pieces'}</span>
                          </div>
                          <div>
                            <span className="text-gray-700 font-medium">Price per carton:</span>
                            <span className="ml-2 text-forest font-medium">₹{parseFloat(item.price).toFixed(2)}</span>
                          </div>
                          {(item as any).pricePerUnit && (
                            <div>
                              <span className="text-gray-700 font-medium">Price per unit:</span>
                              <span className="ml-2 text-forest font-medium">₹{(item as any).pricePerUnit}</span>
                            </div>
                          )}
                          <div>
                            <span className="text-gray-700 font-medium">Total units:</span>
                            <span className="ml-2">{item.quantity * ((item as any).unitsPerCarton || 1)} {(item as any).unitType || 'pieces'}</span>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Pricing Summary */}
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div>
                          <span className="text-gray-700 font-medium">Quantity ordered:</span>
                          <span className="ml-2">{item.quantity} cartons</span>
                        </div>
                        <div>
                          <span className="text-gray-700 font-medium">Price per carton:</span>
                          <span className="ml-2">₹{parseFloat(item.price).toFixed(2)}</span>
                        </div>
                        <div className="col-span-2 pt-2 border-t">
                          <div className="flex justify-between items-center">
                            <span className="font-medium text-gray-900">Line Total:</span>
                            <span className="text-lg font-bold text-forest">
                              ₹{(parseFloat(item.price) * item.quantity).toFixed(2)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <div className="flex justify-between">
                  <span>Subtotal:</span>
                  <span>₹{parseFloat(order.subtotal).toFixed(2)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Tax:</span>
                  <span>₹{parseFloat(order.tax).toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-lg font-semibold">
                  <span>Total:</span>
                  <span className="text-forest">₹{parseFloat(order.total).toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Technical Information */}
          <Card>
            <CardHeader>
              <CardTitle>Technical Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Order ID:</span>
                  <span className="ml-2">#{order.id}</span>
                </div>
                <div>
                  <span className="font-medium">Session ID:</span>
                  <span className="ml-2">{order.sessionId || "N/A"}</span>
                </div>
                <div>
                  <span className="font-medium">User ID:</span>
                  <span className="ml-2">{order.userId || "Guest"}</span>
                </div>
                <div>
                  <span className="font-medium">Seen Status:</span>
                  <Badge variant={order.seen ? "default" : "secondary"} className="ml-2">
                    {order.seen ? "Seen" : "Unseen"}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Orders</h1>
            <p className="text-gray-600">Manage customer orders and track status</p>
          </div>
          <Select value={filter} onValueChange={(value: "all" | "seen" | "unseen") => setFilter(value)}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter orders" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Orders</SelectItem>
              <SelectItem value="unseen">Unseen Orders</SelectItem>
              <SelectItem value="seen">Seen Orders</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>
              {filter === "all" && "All Orders"}
              {filter === "seen" && "Seen Orders"}
              {filter === "unseen" && "Unseen Orders"}
              <span className="ml-2 text-sm font-normal text-gray-500">
                ({filteredOrders.length} orders)
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-4">Loading...</div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Order ID</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Customer</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Items</TableHead>
                    <TableHead>Total</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Seen</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredOrders.map((order) => (
                    <TableRow key={order.id} className={!order.seen ? "bg-blue-50" : ""}>
                      <TableCell>#{order.id}</TableCell>
                      <TableCell>{formatDate(order.createdAt)}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{order.customerName}</div>
                          <div className="text-sm text-gray-500">{order.customerEmail}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="flex items-center space-x-1">
                            <Phone className="h-3 w-3" />
                            <span>{order.customerPhone || "N/A"}</span>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{order.items.length} items</TableCell>
                      <TableCell className="font-semibold text-forest">₹{parseFloat(order.total).toFixed(2)}</TableCell>
                      <TableCell>
                        <Badge variant={order.isGuest ? "secondary" : "default"}>
                          {order.isGuest ? "Guest" : "User"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(order.status)}>
                          {order.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant={order.seen ? "default" : "secondary"}>
                          {order.seen ? "Seen" : "Unseen"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          <OrderDetailsDialog order={order} />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => markSeenMutation.mutate({ id: order.id, seen: !order.seen })}
                            disabled={markSeenMutation.isPending}
                          >
                            {order.seen ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              if (window.confirm("Are you sure you want to delete this order?")) {
                                deleteOrderMutation.mutate(order.id);
                              }
                            }}
                            disabled={deleteOrderMutation.isPending}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
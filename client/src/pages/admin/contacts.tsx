import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AdminLayout } from "@/components/layout/admin-layout";
import { Eye, EyeOff, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import type { Contact } from "@shared/schema";

export default function AdminContacts() {
  const [filter, setFilter] = useState<"all" | "seen" | "unseen">("all");
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const { data: contacts = [], isLoading } = useQuery<Contact[]>({
    queryKey: ["/api/admin/contacts"],
  });

  const markSeenMutation = useMutation({
    mutationFn: async ({ id, seen }: { id: number; seen: boolean }) => {
      const response = await fetch(`/api/admin/contacts/${id}/seen`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ seen }),
      });

      if (!response.ok) {
        throw new Error("Failed to update contact");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/contacts"] });
      toast({
        title: "Contact updated",
        description: "Contact status has been updated successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to update contact status.",
        variant: "destructive",
      });
    },
  });

  const deleteContactMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await fetch(`/api/admin/contacts/${id}`, {
        method: "DELETE",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Failed to delete contact");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/contacts"] });
      toast({
        title: "Contact deleted",
        description: "Contact has been deleted successfully.",
      });
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to delete contact.",
        variant: "destructive",
      });
    },
  });

  const handleDeleteContact = (id: number, name: string) => {
    if (confirm(`Are you sure you want to delete the contact from ${name}? This action cannot be undone.`)) {
      deleteContactMutation.mutate(id);
    }
  };

  const filteredContacts = contacts.filter((contact) => {
    if (filter === "seen") return contact.seen;
    if (filter === "unseen") return !contact.seen;
    return true;
  });

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <AdminLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Contact Inquiries</h1>
            <p className="text-gray-600">Manage customer inquiries and support requests</p>
          </div>
          <Select value={filter} onValueChange={(value: "all" | "seen" | "unseen") => setFilter(value)}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Filter contacts" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Inquiries</SelectItem>
              <SelectItem value="unseen">Unseen Inquiries</SelectItem>
              <SelectItem value="seen">Seen Inquiries</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>
              {filter === "all" && "All Inquiries"}
              {filter === "seen" && "Seen Inquiries"}
              {filter === "unseen" && "Unseen Inquiries"}
              <span className="ml-2 text-sm font-normal text-gray-500">
                ({filteredContacts.length} inquiries)
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="text-center py-4">Loading...</div>
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Phone</TableHead>
                    <TableHead>Company</TableHead>
                    <TableHead>Inquiry Type</TableHead>
                    <TableHead>Message</TableHead>
                    <TableHead>Seen</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredContacts.map((contact) => (
                    <TableRow key={contact.id} className={!contact.seen ? "bg-blue-50" : ""}>
                      <TableCell>{formatDate(contact.createdAt)}</TableCell>
                      <TableCell>{contact.name}</TableCell>
                      <TableCell>{contact.email}</TableCell>
                      <TableCell>{contact.phone || "-"}</TableCell>
                      <TableCell>{contact.company || "-"}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{contact.inquiryType}</Badge>
                      </TableCell>
                      <TableCell className="max-w-xs truncate">{contact.message}</TableCell>
                      <TableCell>
                        <Badge variant={contact.seen ? "default" : "secondary"}>
                          {contact.seen ? "Seen" : "Unseen"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => markSeenMutation.mutate({ id: contact.id, seen: !contact.seen })}
                            disabled={markSeenMutation.isPending}
                            title={contact.seen ? "Mark as unseen" : "Mark as seen"}
                          >
                            {contact.seen ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteContact(contact.id, contact.name)}
                            disabled={deleteContactMutation.isPending}
                            className="text-red-600 hover:text-red-700 hover:border-red-300"
                            title="Delete contact"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            )}
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
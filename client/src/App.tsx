import { Switch, Route, useLocation } from "wouter";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { CartProvider } from "@/components/cart-context";
import { AuthProvider } from "@/components/auth-context";
import { SecureAdminRoute } from "@/components/secure-admin-route";
import { Header } from "@/components/layout/header";
import { Footer } from "@/components/layout/footer";
import { Watermark } from "@/components/layout/watermark";

// Pages
import Home from "@/pages/home";
import Products from "@/pages/products";
import ProductDetail from "@/pages/product-detail";
import About from "@/pages/about";
import Contact from "@/pages/contact";
import Checkout from "@/pages/checkout";
import NotFound from "@/pages/not-found";
import Login from "@/pages/auth/login";
import Register from "@/pages/auth/register";
import AdminDashboard from "@/pages/admin/dashboard";
import AdminOrders from "@/pages/admin/orders";
import AdminContacts from "@/pages/admin/contacts";
import AdminProducts from "@/pages/admin/products";
import AdminSecurity from "@/pages/admin/security";

function Router() {
  return (
    <Switch>
      {/* Public routes */}
      <Route path="/" component={Home} />
      <Route path="/products" component={Products} />
      <Route path="/products/:id" component={ProductDetail} />
      <Route path="/about" component={About} />
      <Route path="/contact" component={Contact} />

      {/* Auth routes */}
      <Route path="/login" component={Login} />
      <Route path="/register" component={Register} />

      {/* Legacy admin login redirect */}
      <Route path="/admin/login">
        {() => {
          window.location.href = "/login";
          return null;
        }}
      </Route>

      {/* Checkout route - no authentication required */}
      <Route path="/checkout" component={Checkout} />
      <Route path="/admin">
        {() => (
          <SecureAdminRoute>
            <AdminDashboard />
          </SecureAdminRoute>
        )}
      </Route>
      <Route path="/admin/orders">
        {() => (
          <SecureAdminRoute>
            <AdminOrders />
          </SecureAdminRoute>
        )}
      </Route>
      <Route path="/admin/contacts">
        {() => (
          <SecureAdminRoute>
            <AdminContacts />
          </SecureAdminRoute>
        )}
      </Route>
      <Route path="/admin/products">
        {() => (
          <SecureAdminRoute>
            <AdminProducts />
          </SecureAdminRoute>
        )}
      </Route>
      <Route path="/admin/security">
        {() => (
          <SecureAdminRoute>
            <AdminSecurity />
          </SecureAdminRoute>
        )}
      </Route>

      <Route component={NotFound} />
    </Switch>
  );
}

function AppContent() {
  const [location] = useLocation();
  const isAdminRoute = location.startsWith('/admin');

  return (
    <div className="min-h-screen flex flex-col relative">
      <Watermark opacity={0.15} size="lg" />
      {!isAdminRoute && <Header />}
      <main className="flex-1 relative z-20">
        <Router />
      </main>
      {!isAdminRoute && <Footer />}
    </div>
  );
}

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <CartProvider>
          <TooltipProvider>
            <AppContent />
            <Toaster />
          </TooltipProvider>
        </CartProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;

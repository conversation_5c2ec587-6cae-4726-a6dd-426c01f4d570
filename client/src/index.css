@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 20 14% 4%;
  --muted: 60 5% 96%;
  --muted-foreground: 25 5% 45%;
  --popover: 0 0% 100%;
  --popover-foreground: 20 14% 4%;
  --card: 0 0% 100%;
  --card-foreground: 20 14% 4%;
  --border: 20 6% 90%;
  --input: 20 6% 90%;
  --primary: 122 39% 49%;
  --primary-foreground: 0 0% 98%;
  --secondary: 60 5% 96%;
  --secondary-foreground: 24 10% 10%;
  --accent: 60 5% 96%;
  --accent-foreground: 24 10% 10%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 60 9% 98%;
  --ring: 20 14% 4%;
  --radius: 0.5rem;
  
  /* Custom eco-friendly colors */
  --forest: 122 39% 49%; /* #2F7D32 - forest green */
  --sage: 88 50% 53%; /* #8BC34A - sage green */
  --warm-brown: 16 29% 34%; /* #6D4C41 - warm brown */
  --earth-brown: 25 29% 49%; /* #A0735A - earth brown */
  --cream: 45 78% 97%; /* #FAF7F2 - cream */
  --off-white: 50 43% 97%; /* #F8F6F0 - off white */
  --terracotta: 14 87% 46%; /* #D84315 - terracotta */
}

.dark {
  --background: 240 10% 4%;
  --foreground: 0 0% 98%;
  --muted: 240 4% 16%;
  --muted-foreground: 240 5% 65%;
  --popover: 240 10% 4%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 4%;
  --card-foreground: 0 0% 98%;
  --border: 240 4% 16%;
  --input: 240 4% 16%;
  --primary: 122 39% 49%;
  --primary-foreground: 0 0% 98%;
  --secondary: 240 4% 16%;
  --secondary-foreground: 0 0% 98%;
  --accent: 240 4% 16%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 63% 31%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 5% 84%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', sans-serif;
  }

  .font-playfair {
    font-family: 'Playfair Display', serif;
  }
}

@layer utilities {
  .animate-fade-in {
    animation: fadeIn 1s ease-in-out;
  }

  .animate-fade-in-right {
    animation: fadeInRight 1s ease-in-out;
  }

  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
  }

  .border-3 {
    border-width: 3px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@layer utilities {
  .text-forest {
    color: hsl(var(--forest));
  }
  
  .text-sage {
    color: hsl(var(--sage));
  }
  
  .text-warm-brown {
    color: hsl(var(--warm-brown));
  }
  
  .text-earth-brown {
    color: hsl(var(--earth-brown));
  }
  
  .text-terracotta {
    color: hsl(var(--terracotta));
  }
  
  .bg-forest {
    background-color: hsl(var(--forest));
  }
  
  .bg-sage {
    background-color: hsl(var(--sage));
  }
  
  .bg-warm-brown {
    background-color: hsl(var(--warm-brown));
  }
  
  .bg-earth-brown {
    background-color: hsl(var(--earth-brown));
  }
  
  .bg-cream {
    background-color: hsl(var(--cream));
  }
  
  .bg-off-white {
    background-color: hsl(var(--off-white));
  }
  
  .bg-terracotta {
    background-color: hsl(var(--terracotta));
  }
  
  .border-forest {
    border-color: hsl(var(--forest));
  }
  
  .hover\:bg-forest:hover {
    background-color: hsl(var(--forest));
  }
  
  .hover\:bg-sage:hover {
    background-color: hsl(var(--sage));
  }
  
  .hover\:text-forest:hover {
    color: hsl(var(--forest));
  }
  
  .hover\:text-sage:hover {
    color: hsl(var(--sage));
  }
}

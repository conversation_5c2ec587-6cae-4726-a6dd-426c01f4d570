#!/usr/bin/env node

/**
 * Update admin password in the database to the new secure password
 */

import { Pool } from '@neondatabase/serverless';
import bcrypt from 'bcrypt';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require'
});

async function updateAdminPassword() {
  console.log('🔐 Updating Admin Password in Database...\n');

  try {
    // 1. Check current admin user
    console.log('1. Checking current admin user...');
    const currentAdmin = await pool.query(
      'SELECT id, username, email, role, two_factor_enabled FROM users WHERE username = $1 OR role = $2',
      ['admin', 'admin']
    );

    if (currentAdmin.rows.length === 0) {
      console.log('   ❌ No admin user found in database');
      return;
    }

    console.log('   ✅ Found admin user(s):');
    currentAdmin.rows.forEach(user => {
      console.log(`      - ID: ${user.id}, Username: ${user.username}, Email: ${user.email}, Role: ${user.role}, 2FA: ${user.two_factor_enabled}`);
    });

    // 2. Generate new password hash
    console.log('\n2. Generating new password hash...');
    const newPassword = "eco-grovea#Admin#";
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    console.log(`   New Password: ${newPassword}`);
    console.log(`   Hash: ${hashedPassword.substring(0, 20)}...`);

    // 3. Update admin password
    console.log('\n3. Updating admin password...');
    const updateResult = await pool.query(
      `UPDATE users 
       SET password = $1, 
           password_changed_at = NOW(),
           updated_at = NOW(),
           login_attempts = 0,
           locked_until = NULL
       WHERE username = 'admin' OR role = 'admin'
       RETURNING id, username, email, role`,
      [hashedPassword]
    );

    if (updateResult.rows.length > 0) {
      console.log('   ✅ Admin password updated successfully!');
      updateResult.rows.forEach(user => {
        console.log(`      - Updated user: ${user.username} (ID: ${user.id})`);
      });
    } else {
      console.log('   ❌ No admin users were updated');
    }

    // 4. Verify the new password works
    console.log('\n4. Verifying new password...');
    const verifyResult = await pool.query(
      'SELECT id, username, password FROM users WHERE username = $1',
      ['admin']
    );

    if (verifyResult.rows.length > 0) {
      const user = verifyResult.rows[0];
      const isValid = await bcrypt.compare(newPassword, user.password);
      
      if (isValid) {
        console.log('   ✅ Password verification successful!');
      } else {
        console.log('   ❌ Password verification failed!');
      }
    }

    console.log('\n🎉 Admin Password Update Complete!');
    
    console.log('\n📋 New Admin Credentials:');
    console.log(`   Username: admin`);
    console.log(`   Password: eco-grovea#Admin#`);
    console.log(`   Email: <EMAIL>`);
    console.log(`   Role: admin`);
    
    console.log('\n🔒 Security Notes:');
    console.log(`   ✅ Password is properly hashed with bcrypt (12 rounds)`);
    console.log(`   ✅ Login attempts reset to 0`);
    console.log(`   ✅ Account unlocked (locked_until cleared)`);
    console.log(`   ✅ Password change timestamp updated`);
    
    console.log('\n🚀 Ready for Use:');
    console.log(`   - Login at: http://localhost:3001/login`);
    console.log(`   - Use new credentials to access admin panel`);
    console.log(`   - 2FA will be required if enabled`);

  } catch (error) {
    console.error('❌ Password update failed:', error.message);
    console.error('Stack:', error.stack);
  } finally {
    await pool.end();
  }
}

// Run the update
updateAdminPassword();

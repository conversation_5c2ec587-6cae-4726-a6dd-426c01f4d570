#!/usr/bin/env node

/**
 * Test script to verify admin login functionality
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

// Store session cookie
let sessionCookie = null;

async function makeRequest(method, url, body = null, headers = {}) {
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  };
  
  // Add session cookie if we have one
  if (sessionCookie) {
    options.headers['Cookie'] = sessionCookie;
  }
  
  if (body) {
    options.body = JSON.stringify(body);
  }
  
  const response = await fetch(`${BASE_URL}${url}`, options);
  
  // Extract session cookie from first response
  if (!sessionCookie && response.headers.get('set-cookie')) {
    sessionCookie = response.headers.get('set-cookie').split(';')[0];
  }
  
  let data;
  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    data = await response.json();
  } else {
    data = await response.text();
  }
  
  return { status: response.status, data, headers: response.headers };
}

async function testAdminLogin() {
  console.log('🔐 Testing Admin Login Functionality...\n');
  
  try {
    // 1. Get CSRF token
    console.log('1. Getting CSRF token...');
    const csrfResult = await makeRequest('GET', '/api/auth/csrf-token');
    console.log(`   Status: ${csrfResult.status}`);
    
    if (csrfResult.status !== 200) {
      throw new Error(`Failed to get CSRF token: ${csrfResult.data}`);
    }
    
    const csrfToken = csrfResult.data.csrfToken;
    console.log(`   CSRF Token: ${csrfToken.substring(0, 16)}...`);
    
    // 2. Test admin login
    console.log('\n2. Testing admin login...');
    const loginResult = await makeRequest('POST', '/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    }, {
      'X-CSRF-Token': csrfToken
    });
    
    console.log(`   Status: ${loginResult.status}`);
    console.log(`   Response:`, loginResult.data);
    
    if (loginResult.status === 200) {
      console.log('\n✅ SUCCESS: Admin login working correctly!');
      
      if (loginResult.data.requiresTwoFactor) {
        console.log('   📱 2FA is required for this admin user');
        console.log('   💡 Setup 2FA to complete the login process');
      } else {
        console.log('   🔓 Login completed without 2FA');
      }
    } else {
      console.log('\n❌ Login failed:', loginResult.data.message);
    }
    
    // 3. Test auth status
    console.log('\n3. Checking auth status...');
    const statusResult = await makeRequest('GET', '/api/auth/status');
    console.log(`   Status: ${statusResult.status}`);
    console.log(`   Auth Status:`, statusResult.data);
    
    console.log('\n🎉 Admin login test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testAdminLogin();

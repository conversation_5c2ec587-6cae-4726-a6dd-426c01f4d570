#!/usr/bin/env node

/**
 * Test script to simulate frontend login flow and identify issues
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

async function testFrontendLoginFlow() {
  console.log('🔍 Testing Frontend Login Flow Simulation...\n');
  
  try {
    // Simulate the exact flow that the frontend AuthContext would do
    
    // 1. Get CSRF token (like useEffect in login page)
    console.log('1. Getting CSRF token (like frontend useEffect)...');
    const csrfResponse = await fetch(`${BASE_URL}/api/auth/csrf-token`, {
      credentials: 'include',
    });
    
    const { csrfToken } = await csrfResponse.json();
    const sessionCookie = csrfResponse.headers.get('set-cookie')?.split(';')[0];
    console.log(`   ✅ CSRF Token: ${csrfToken.substring(0, 16)}...`);
    
    // 2. Check initial auth status (like AuthContext query)
    console.log('\n2. Checking initial auth status (like AuthContext)...');
    const initialStatus = await fetch(`${BASE_URL}/api/auth/status`, {
      headers: { 'Cookie': sessionCookie || '' },
      credentials: 'include',
    });
    
    const initialData = await initialStatus.json();
    console.log(`   Initial Auth State:`, JSON.stringify(initialData, null, 2));
    
    // 3. Simulate login mutation (like AuthContext loginMutation)
    console.log('\n3. Simulating login mutation (like AuthContext)...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
        'Cookie': sessionCookie || '',
      },
      credentials: 'include',
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123',
      }),
    });
    
    const loginData = await loginResponse.json();
    console.log(`   Login Response:`, JSON.stringify(loginData, null, 2));
    
    // 4. Check auth status after login (like AuthContext would refetch)
    console.log('\n4. Checking auth status after login (like AuthContext refetch)...');
    const postLoginStatus = await fetch(`${BASE_URL}/api/auth/status`, {
      headers: { 'Cookie': sessionCookie || '' },
      credentials: 'include',
    });
    
    const postLoginData = await postLoginStatus.json();
    console.log(`   Post-Login Auth State:`, JSON.stringify(postLoginData, null, 2));
    
    // 5. Analyze the expected frontend state
    console.log('\n5. Analyzing expected frontend state...');
    
    const expectedFrontendState = {
      isAuthenticated: postLoginData.authenticated,
      user: postLoginData.user,
      requiresTwoFactor: loginData.requiresTwoFactor,
      twoFactorVerified: postLoginData.twoFactorVerified,
      pendingTwoFactor: postLoginData.pendingTwoFactor,
      twoFactorEnabled: postLoginData.twoFactorEnabled,
    };
    
    console.log(`   Expected Frontend State:`, JSON.stringify(expectedFrontendState, null, 2));
    
    // 6. Check what should happen in the UI
    console.log('\n6. UI State Analysis:');
    console.log(`   Should show login form: ${!expectedFrontendState.isAuthenticated}`);
    console.log(`   Should show 2FA form: ${expectedFrontendState.pendingTwoFactor}`);
    console.log(`   Should show admin panel: ${expectedFrontendState.isAuthenticated && expectedFrontendState.twoFactorVerified}`);
    console.log(`   2FA Status Display: ${expectedFrontendState.twoFactorEnabled ? 'Enabled' : 'Not Enabled'}`);
    
    // 7. Test 2FA verification flow
    if (expectedFrontendState.pendingTwoFactor) {
      console.log('\n7. Testing 2FA verification flow...');
      
      // Try with dummy code (will fail)
      const verify2FAResponse = await fetch(`${BASE_URL}/api/auth/verify-2fa`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken,
          'Cookie': sessionCookie || '',
        },
        credentials: 'include',
        body: JSON.stringify({
          token: '123456', // Dummy code
        }),
      });
      
      const verify2FAData = await verify2FAResponse.json();
      console.log(`   2FA Verification Status: ${verify2FAResponse.status}`);
      console.log(`   2FA Response: ${verify2FAData.message}`);
      
      if (verify2FAResponse.status === 401) {
        console.log(`   ✅ 2FA correctly rejects invalid codes`);
        console.log(`   💡 User would need to enter real code from Google Authenticator`);
      }
    }
    
    console.log('\n🎯 Analysis Complete!');
    
    console.log('\n📋 Summary:');
    console.log(`   ✅ Backend Login: Working`);
    console.log(`   ✅ 2FA Detection: Working`);
    console.log(`   ✅ Auth Status: Working`);
    console.log(`   ✅ 2FA Status Display: Should show "Enabled"`);
    
    if (expectedFrontendState.pendingTwoFactor) {
      console.log('\n🔐 Current State:');
      console.log(`   - User is logged in but needs 2FA verification`);
      console.log(`   - Frontend should show 2FA input form`);
      console.log(`   - Admin panel should show "2FA Enabled" status`);
      console.log(`   - After 2FA verification, user gets full access`);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testFrontendLoginFlow();

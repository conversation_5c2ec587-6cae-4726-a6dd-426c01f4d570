#!/usr/bin/env node

/**
 * Test script to verify registration works without CSR<PERSON> token
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

async function testRegistrationWithoutCSRF() {
  console.log('📝 Testing Registration Without CSRF Token...\n');
  
  try {
    // 1. Test registration without CSRF token (should work now)
    console.log('1. Testing registration without CSRF token...');
    
    const testUser = {
      username: `testuser_${Date.now()}`,
      email: `test_${Date.now()}@example.com`,
      password: 'testpassword123',
      firstName: 'Test',
      lastName: 'User'
    };
    
    console.log(`   Test User: ${testUser.username} (${testUser.email})`);
    
    const registrationResponse = await fetch(`${BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Intentionally NOT including X-CSRF-Token header
      },
      credentials: 'include',
      body: JSON.stringify(testUser),
    });
    
    console.log(`   Registration Status: ${registrationResponse.status}`);
    
    if (registrationResponse.ok) {
      const registrationData = await registrationResponse.json();
      console.log(`   ✅ Registration Successful!`);
      console.log(`   Response:`, JSON.stringify(registrationData, null, 2));
    } else {
      const errorData = await registrationResponse.text();
      console.log(`   ❌ Registration Failed: ${errorData}`);
      
      if (errorData.includes('Invalid CSRF token')) {
        console.log(`   🚨 CSRF token still required - fix needed!`);
      } else {
        console.log(`   💡 Different error - might be expected (duplicate user, etc.)`);
      }
    }
    
    // 2. Test that CSRF is still required for login (should still be protected)
    console.log('\n2. Testing that login still requires CSRF token...');
    
    const loginWithoutCSRF = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Intentionally NOT including X-CSRF-Token header
      },
      credentials: 'include',
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123',
      }),
    });
    
    console.log(`   Login Without CSRF Status: ${loginWithoutCSRF.status}`);
    
    if (loginWithoutCSRF.status === 403) {
      const loginError = await loginWithoutCSRF.text();
      if (loginError.includes('Invalid CSRF token')) {
        console.log(`   ✅ Login still properly protected by CSRF`);
      }
    } else {
      console.log(`   ⚠️  Login might not be properly protected by CSRF`);
    }
    
    // 3. Test CSRF token endpoint (should work without CSRF)
    console.log('\n3. Testing CSRF token endpoint...');
    
    const csrfResponse = await fetch(`${BASE_URL}/api/auth/csrf-token`, {
      credentials: 'include',
    });
    
    console.log(`   CSRF Token Status: ${csrfResponse.status}`);
    
    if (csrfResponse.ok) {
      const { csrfToken } = await csrfResponse.json();
      console.log(`   ✅ CSRF Token Retrieved: ${csrfToken.substring(0, 16)}...`);
      
      // 4. Test login with proper CSRF token (should work)
      console.log('\n4. Testing login with proper CSRF token...');
      
      const sessionCookie = csrfResponse.headers.get('set-cookie')?.split(';')[0];
      
      const loginWithCSRF = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken,
          'Cookie': sessionCookie || '',
        },
        credentials: 'include',
        body: JSON.stringify({
          username: 'admin',
          password: 'admin123',
        }),
      });
      
      console.log(`   Login With CSRF Status: ${loginWithCSRF.status}`);
      
      if (loginWithCSRF.ok) {
        const loginData = await loginWithCSRF.json();
        console.log(`   ✅ Login with CSRF successful`);
        console.log(`   2FA Required: ${loginData.requiresTwoFactor ? 'YES' : 'NO'}`);
      } else {
        const loginError = await loginWithCSRF.text();
        console.log(`   ❌ Login with CSRF failed: ${loginError}`);
      }
    } else {
      console.log(`   ❌ CSRF Token retrieval failed`);
    }
    
    console.log('\n🎉 Registration CSRF Test Complete!');
    
    console.log('\n📋 Test Results Summary:');
    console.log(`   ✅ Registration: ${registrationResponse.ok ? 'Works without CSRF' : 'Still blocked by CSRF'}`);
    console.log(`   ✅ Login Protection: ${loginWithoutCSRF.status === 403 ? 'Still protected by CSRF' : 'Not protected'}`);
    console.log(`   ✅ CSRF Token Endpoint: ${csrfResponse.ok ? 'Working' : 'Not working'}`);
    
    console.log('\n🔒 Security Status:');
    console.log(`   ✅ Public endpoints (register): No CSRF required`);
    console.log(`   ✅ Protected endpoints (login): CSRF required`);
    console.log(`   ✅ Token endpoint: Accessible without CSRF`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testRegistrationWithoutCSRF();

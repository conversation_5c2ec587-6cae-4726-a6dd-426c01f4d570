#!/usr/bin/env node

/**
 * Database migration script to add security-related columns and tables
 * This script adds 2FA support and security logging to the existing database
 */

import { Pool } from '@neondatabase/serverless';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require'
});

async function runMigration() {
  console.log('🔄 Starting security schema migration...\n');

  try {
    // Add security columns to users table
    console.log('1. Adding security columns to users table...');
    
    const userMigrations = [
      // 2FA fields
      `ALTER TABLE users ADD COLUMN IF NOT EXISTS two_factor_secret TEXT`,
      `ALTER TABLE users ADD COLUMN IF NOT EXISTS two_factor_enabled BOOLEAN NOT NULL DEFAULT false`,
      `ALTER TABLE users ADD COLUMN IF NOT EXISTS two_factor_backup_codes JSONB`,
      
      // Security fields
      `ALTER TABLE users ADD COLUMN IF NOT EXISTS password_changed_at TIMESTAMP`,
      `ALTER TABLE users ADD COLUMN IF NOT EXISTS security_questions JSONB`,
    ];

    for (const migration of userMigrations) {
      await pool.query(migration);
      console.log(`   ✅ ${migration.split('ADD COLUMN IF NOT EXISTS')[1]?.split(' ')[0] || 'Migration'} added`);
    }

    // Create security_logs table
    console.log('\n2. Creating security_logs table...');
    
    const createSecurityLogsTable = `
      CREATE TABLE IF NOT EXISTS security_logs (
        id SERIAL PRIMARY KEY,
        user_id INTEGER REFERENCES users(id),
        event_type TEXT NOT NULL,
        ip_address TEXT NOT NULL,
        user_agent TEXT,
        details JSONB,
        severity TEXT NOT NULL DEFAULT 'INFO',
        created_at TIMESTAMP NOT NULL DEFAULT NOW()
      )
    `;

    await pool.query(createSecurityLogsTable);
    console.log('   ✅ security_logs table created');

    // Create indexes for better performance
    console.log('\n3. Creating indexes...');
    
    const indexes = [
      `CREATE INDEX IF NOT EXISTS idx_security_logs_user_id ON security_logs(user_id)`,
      `CREATE INDEX IF NOT EXISTS idx_security_logs_event_type ON security_logs(event_type)`,
      `CREATE INDEX IF NOT EXISTS idx_security_logs_created_at ON security_logs(created_at)`,
      `CREATE INDEX IF NOT EXISTS idx_security_logs_severity ON security_logs(severity)`,
      `CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)`,
      `CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)`,
      `CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)`,
    ];

    for (const index of indexes) {
      await pool.query(index);
      console.log(`   ✅ ${index.split('idx_')[1]?.split(' ')[0] || 'Index'} created`);
    }

    // Update existing admin user with default security settings
    console.log('\n4. Updating existing admin user...');
    
    const updateAdminQuery = `
      UPDATE users 
      SET 
        password_changed_at = COALESCE(password_changed_at, created_at),
        two_factor_enabled = COALESCE(two_factor_enabled, false)
      WHERE role = 'admin'
    `;

    const result = await pool.query(updateAdminQuery);
    console.log(`   ✅ Updated ${result.rowCount} admin user(s)`);

    // Insert initial security log entry
    console.log('\n5. Creating initial security log entry...');
    
    const initialLogQuery = `
      INSERT INTO security_logs (event_type, ip_address, details, severity)
      VALUES ('SYSTEM_MIGRATION', '127.0.0.1', '{"migration": "security_schema_v1", "timestamp": "${new Date().toISOString()}"}', 'INFO')
    `;

    await pool.query(initialLogQuery);
    console.log('   ✅ Initial security log entry created');

    console.log('\n🎉 Security schema migration completed successfully!');
    console.log('\n📋 Summary of changes:');
    console.log('   • Added 2FA support columns to users table');
    console.log('   • Added security tracking columns to users table');
    console.log('   • Created security_logs table for audit trail');
    console.log('   • Created performance indexes');
    console.log('   • Updated existing admin users');
    console.log('   • Added initial security log entry');

    console.log('\n🔐 Next steps:');
    console.log('   1. Restart your application server');
    console.log('   2. Login to admin panel');
    console.log('   3. Setup 2FA for enhanced security');
    console.log('   4. Monitor security logs regularly');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    
    if (error.code === '42P01') {
      console.error('\n💡 Tip: Make sure the users table exists before running this migration.');
    } else if (error.code === '42703') {
      console.error('\n💡 Tip: Some columns might already exist. This is normal for partial migrations.');
    }
    
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Verify database connection first
async function verifyConnection() {
  try {
    const result = await pool.query('SELECT 1 as test');
    console.log('✅ Database connection verified');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Main execution
(async () => {
  console.log('🔒 EcoGrovea Security Schema Migration');
  console.log('=====================================\n');

  const connected = await verifyConnection();
  if (!connected) {
    console.error('Please check your database connection and try again.');
    process.exit(1);
  }

  await runMigration();
})();

#!/usr/bin/env node

/**
 * Test script to verify logout functionality
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

async function testLogout() {
  console.log('🚪 Testing Logout Functionality...\n');
  
  try {
    // 1. Login first
    console.log('1. Logging in as admin...');
    
    // Get CSRF token
    const csrfResponse = await fetch(`${BASE_URL}/api/auth/csrf-token`, {
      credentials: 'include',
    });
    const { csrfToken } = await csrfResponse.json();
    const sessionCookie = csrfResponse.headers.get('set-cookie')?.split(';')[0];
    
    // Login
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
        'Cookie': sessionCookie || '',
      },
      credentials: 'include',
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123',
      }),
    });
    
    if (!loginResponse.ok) {
      throw new Error('Login failed');
    }
    
    console.log('   ✅ Login successful');
    
    // 2. Verify we're authenticated
    console.log('\n2. Verifying authentication...');
    const statusResponse = await fetch(`${BASE_URL}/api/auth/status`, {
      headers: { 'Cookie': sessionCookie || '' },
      credentials: 'include',
    });
    
    const statusData = await statusResponse.json();
    console.log(`   Authenticated: ${statusData.authenticated}`);
    console.log(`   Is Admin: ${statusData.isAdmin}`);
    
    if (!statusData.authenticated) {
      throw new Error('Not authenticated after login');
    }
    
    // 3. Test logout
    console.log('\n3. Testing logout...');
    
    // Get fresh CSRF token for logout
    const logoutCsrfResponse = await fetch(`${BASE_URL}/api/auth/csrf-token`, {
      headers: { 'Cookie': sessionCookie || '' },
      credentials: 'include',
    });
    const { csrfToken: logoutCsrfToken } = await logoutCsrfResponse.json();
    
    const logoutResponse = await fetch(`${BASE_URL}/api/auth/logout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': logoutCsrfToken,
        'Cookie': sessionCookie || '',
      },
      credentials: 'include',
    });
    
    const logoutData = await logoutResponse.json();
    console.log(`   Status: ${logoutResponse.status}`);
    console.log(`   Message: ${logoutData.message}`);
    
    if (logoutResponse.ok) {
      console.log('   ✅ Logout successful');
    } else {
      console.log('   ❌ Logout failed');
      return;
    }
    
    // 4. Verify we're logged out
    console.log('\n4. Verifying logout...');
    const postLogoutStatusResponse = await fetch(`${BASE_URL}/api/auth/status`, {
      headers: { 'Cookie': sessionCookie || '' },
      credentials: 'include',
    });
    
    const postLogoutStatusData = await postLogoutStatusResponse.json();
    console.log(`   Authenticated: ${postLogoutStatusData.authenticated}`);
    console.log(`   Is Admin: ${postLogoutStatusData.isAdmin}`);
    
    if (!postLogoutStatusData.authenticated) {
      console.log('   ✅ Successfully logged out');
    } else {
      console.log('   ❌ Still authenticated after logout');
    }
    
    console.log('\n🎉 Logout test completed!');
    
    if (logoutResponse.ok && !postLogoutStatusData.authenticated) {
      console.log('\n✅ RESULT: Logout functionality is working correctly!');
    } else {
      console.log('\n❌ RESULT: Logout functionality needs attention');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testLogout();

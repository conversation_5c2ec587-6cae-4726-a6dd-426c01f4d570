#!/usr/bin/env node

/**
 * Debug script to identify login issues
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

async function debugLoginIssues() {
  console.log('🔍 Debugging Login Issues...\n');
  
  try {
    // 1. Test CSRF token endpoint
    console.log('1. Testing CSRF token endpoint...');
    const csrfResponse = await fetch(`${BASE_URL}/api/auth/csrf-token`, {
      credentials: 'include',
    });
    
    console.log(`   CSRF Status: ${csrfResponse.status}`);
    
    if (csrfResponse.ok) {
      const { csrfToken } = await csrfResponse.json();
      const sessionCookie = csrfResponse.headers.get('set-cookie')?.split(';')[0];
      
      console.log(`   ✅ CSRF Token: ${csrfToken.substring(0, 16)}...`);
      console.log(`   ✅ Session Cookie: ${sessionCookie ? 'Set' : 'Not set'}`);
      
      // 2. Test auth status endpoint
      console.log('\n2. Testing auth status endpoint...');
      const statusResponse = await fetch(`${BASE_URL}/api/auth/status`, {
        headers: { 'Cookie': sessionCookie || '' },
        credentials: 'include',
      });
      
      console.log(`   Status Code: ${statusResponse.status}`);
      
      if (statusResponse.ok) {
        const statusData = await statusResponse.json();
        console.log(`   ✅ Auth Status Response:`, JSON.stringify(statusData, null, 2));
      } else {
        console.log(`   ❌ Auth Status Failed: ${await statusResponse.text()}`);
      }
      
      // 3. Test login endpoint
      console.log('\n3. Testing login endpoint...');
      const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': csrfToken,
          'Cookie': sessionCookie || '',
        },
        credentials: 'include',
        body: JSON.stringify({
          username: 'admin',
          password: 'admin123',
        }),
      });
      
      console.log(`   Login Status: ${loginResponse.status}`);
      
      if (loginResponse.ok) {
        const loginData = await loginResponse.json();
        console.log(`   ✅ Login Response:`, JSON.stringify(loginData, null, 2));
        
        // 4. Test auth status after login
        console.log('\n4. Testing auth status after login...');
        const postLoginStatus = await fetch(`${BASE_URL}/api/auth/status`, {
          headers: { 'Cookie': sessionCookie || '' },
          credentials: 'include',
        });
        
        if (postLoginStatus.ok) {
          const postLoginData = await postLoginStatus.json();
          console.log(`   ✅ Post-Login Auth Status:`, JSON.stringify(postLoginData, null, 2));
        } else {
          console.log(`   ❌ Post-Login Auth Status Failed: ${await postLoginStatus.text()}`);
        }
        
      } else {
        const loginError = await loginResponse.text();
        console.log(`   ❌ Login Failed: ${loginError}`);
      }
      
    } else {
      console.log(`   ❌ CSRF Token Failed: ${await csrfResponse.text()}`);
    }
    
    console.log('\n🔍 Debug Complete!');
    
  } catch (error) {
    console.error('❌ Debug failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the debug
debugLoginIssues();

#!/usr/bin/env node

/**
 * Test script to verify the fixed login and complete 2FA system
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

async function testFixedLoginAnd2FA() {
  console.log('🔐 Testing Fixed Login and Complete 2FA System...\n');
  
  try {
    // 1. Test CSRF token retrieval
    console.log('1. Testing CSRF token retrieval...');
    const csrfResponse = await fetch(`${BASE_URL}/api/auth/csrf-token`, {
      credentials: 'include',
    });
    
    if (!csrfResponse.ok) {
      throw new Error('Failed to get CSRF token');
    }
    
    const { csrfToken } = await csrfResponse.json();
    const sessionCookie = csrfResponse.headers.get('set-cookie')?.split(';')[0];
    
    console.log(`   ✅ CSRF Token: ${csrfToken.substring(0, 16)}...`);
    console.log(`   ✅ Session Cookie: ${sessionCookie ? 'Set' : 'Not set'}`);
    
    // 2. Test admin login with proper CSRF token
    console.log('\n2. Testing admin login with CSRF protection...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
        'Cookie': sessionCookie || '',
      },
      credentials: 'include',
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123',
      }),
    });
    
    const loginData = await loginResponse.json();
    console.log(`   Status: ${loginResponse.status}`);
    console.log(`   Message: ${loginData.message}`);
    console.log(`   2FA Required: ${loginData.requiresTwoFactor ? 'YES' : 'NO'}`);
    
    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginData.message}`);
    }
    
    console.log('   ✅ Admin login successful!');
    
    // 3. Setup 2FA and get QR code
    console.log('\n3. Setting up 2FA and generating QR code...');
    const setup2FAResponse = await fetch(`${BASE_URL}/api/auth/setup-2fa`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
        'Cookie': sessionCookie || '',
      },
      credentials: 'include',
    });
    
    const setup2FAData = await setup2FAResponse.json();
    console.log(`   Status: ${setup2FAResponse.status}`);
    
    if (setup2FAResponse.ok) {
      console.log('   ✅ 2FA Setup successful!');
      console.log(`   📱 QR Code: ${setup2FAData.qrCode ? 'Generated (base64 image)' : 'Not generated'}`);
      console.log(`   🔑 Manual Key: ${setup2FAData.manualEntryKey}`);
      console.log(`   💾 Backup Codes: ${setup2FAData.backupCodes?.length || 0} codes generated`);
      
      // Save QR code to file for testing
      if (setup2FAData.qrCode) {
        console.log('\n   📋 QR Code Details:');
        console.log(`   - Format: ${setup2FAData.qrCode.startsWith('data:image/png;base64,') ? 'PNG Base64' : 'Unknown'}`);
        console.log(`   - Size: ${setup2FAData.qrCode.length} characters`);
        console.log('   - Ready to scan with Google Authenticator!');
      }
      
      if (setup2FAData.backupCodes) {
        console.log('\n   🔐 Backup Codes (save these securely):');
        setup2FAData.backupCodes.forEach((code, index) => {
          console.log(`   ${index + 1}. ${code}`);
        });
      }
    } else {
      console.log(`   ❌ 2FA Setup failed: ${setup2FAData.message}`);
    }
    
    // 4. Test auth status
    console.log('\n4. Checking authentication status...');
    const statusResponse = await fetch(`${BASE_URL}/api/auth/status`, {
      headers: { 'Cookie': sessionCookie || '' },
      credentials: 'include',
    });
    
    const statusData = await statusResponse.json();
    console.log(`   Authenticated: ${statusData.authenticated}`);
    console.log(`   Is Admin: ${statusData.isAdmin}`);
    console.log(`   2FA Verified: ${statusData.twoFactorVerified}`);
    console.log(`   Pending 2FA: ${statusData.pendingTwoFactor}`);
    
    // 5. Test logout
    console.log('\n5. Testing logout...');
    const logoutResponse = await fetch(`${BASE_URL}/api/auth/logout`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
        'Cookie': sessionCookie || '',
      },
      credentials: 'include',
    });
    
    const logoutData = await logoutResponse.json();
    console.log(`   Status: ${logoutResponse.status}`);
    console.log(`   Message: ${logoutData.message}`);
    
    // 6. Verify logout worked
    const postLogoutStatus = await fetch(`${BASE_URL}/api/auth/status`, {
      headers: { 'Cookie': sessionCookie || '' },
      credentials: 'include',
    });
    
    const postLogoutData = await postLogoutStatus.json();
    console.log(`   Post-logout authenticated: ${postLogoutData.authenticated}`);
    
    console.log('\n🎉 All Tests Completed Successfully!');
    
    console.log('\n📋 Test Results Summary:');
    console.log('   ✅ CSRF Token Retrieval: Working');
    console.log('   ✅ CSRF Protection: Working');
    console.log('   ✅ Admin Login: Working');
    console.log('   ✅ 2FA Setup: Working');
    console.log('   ✅ QR Code Generation: Working');
    console.log('   ✅ Manual Entry Key: Working');
    console.log('   ✅ Backup Codes: Working');
    console.log('   ✅ Session Management: Working');
    console.log('   ✅ Logout: Working');
    
    console.log('\n🚀 Ready for 2FA Setup!');
    console.log('\n📱 Next Steps to Enable 2FA:');
    console.log('   1. Open http://localhost:3000/login');
    console.log('   2. Click "Admin" tab');
    console.log('   3. Login: admin / admin123');
    console.log('   4. Go to Admin Panel → Security');
    console.log('   5. Click "Setup 2FA"');
    console.log('   6. Scan QR code with Google Authenticator');
    console.log('   7. Enter 6-digit code to verify and enable 2FA');
    console.log('   8. Download and save backup codes');
    console.log('   9. Test login with 2FA enabled');
    
    console.log('\n🔐 Manual Entry Key for Google Authenticator:');
    if (setup2FAData?.manualEntryKey) {
      console.log(`   Key: ${setup2FAData.manualEntryKey}`);
      console.log('   Account: EcoGrovea Admin');
      console.log('   Type: Time-based (TOTP)');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Make sure the server is running on port 5000');
    console.log('   2. Check that the admin user exists in the database');
    console.log('   3. Verify CSRF protection is working correctly');
    console.log('   4. Check server logs for detailed error information');
  }
}

// Run the comprehensive test
testFixedLoginAnd2FA();

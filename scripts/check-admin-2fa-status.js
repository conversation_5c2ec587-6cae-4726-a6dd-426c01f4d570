#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to check admin user's 2FA status
 */

import { Pool } from '@neondatabase/serverless';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require'
});

async function checkAdmin2FAStatus() {
  console.log('🔍 Checking Admin 2FA Status...\n');

  try {
    // Check admin user's 2FA status
    const result = await pool.query(
      `SELECT 
        id, username, email, role, 
        two_factor_enabled, 
        two_factor_secret IS NOT NULL as has_2fa_secret,
        two_factor_backup_codes IS NOT NULL as has_backup_codes,
        created_at, updated_at
       FROM users 
       WHERE username = 'admin' OR role = 'admin'`
    );

    if (result.rows.length === 0) {
      console.log('❌ No admin user found!');
      return;
    }

    console.log('👤 Admin User Status:');
    result.rows.forEach(user => {
      console.log(`   ID: ${user.id}`);
      console.log(`   Username: ${user.username}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Role: ${user.role}`);
      console.log(`   2FA Enabled: ${user.two_factor_enabled ? '✅ YES' : '❌ NO'}`);
      console.log(`   Has 2FA Secret: ${user.has_2fa_secret ? '✅ YES' : '❌ NO'}`);
      console.log(`   Has Backup Codes: ${user.has_backup_codes ? '✅ YES' : '❌ NO'}`);
      console.log(`   Created: ${user.created_at}`);
      console.log(`   Updated: ${user.updated_at}`);
    });

    console.log('\n📋 2FA Status Summary:');
    const admin = result.rows[0];
    
    if (!admin.two_factor_enabled) {
      console.log('   🔓 2FA is NOT enabled for admin user');
      console.log('   💡 This is why login doesn\'t require 2FA token');
      console.log('   🔐 To enable 2FA: Login to admin panel → Security → Setup 2FA');
    } else {
      console.log('   🔒 2FA is enabled for admin user');
      console.log('   📱 Login should require 2FA token');
      
      if (!admin.has_2fa_secret) {
        console.log('   ⚠️  WARNING: 2FA enabled but no secret found!');
      }
    }

    console.log('\n🚀 Next Steps:');
    console.log('   1. Login to admin panel: http://localhost:3000/admin');
    console.log('   2. Username: admin, Password: admin123');
    console.log('   3. Go to Security section');
    console.log('   4. Setup 2FA to enable token verification');

  } catch (error) {
    console.error('❌ Failed to check admin 2FA status:', error);
  } finally {
    await pool.end();
  }
}

// Run the check
checkAdmin2FAStatus();

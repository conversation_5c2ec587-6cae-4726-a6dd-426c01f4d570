#!/usr/bin/env node

/**
 * Generate bcrypt hash for the new admin password
 */

import bcrypt from 'bcrypt';

async function generatePasswordHash() {
  console.log('🔐 Generating bcrypt hash for new admin password...\n');
  
  try {
    const newPassword = "eco-grovea#Admin#";
    const saltRounds = 12;
    
    console.log(`Password: ${newPassword}`);
    console.log(`Salt Rounds: ${saltRounds}`);
    
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
    
    console.log(`\n✅ Generated Hash:`);
    console.log(`${hashedPassword}`);
    
    // Verify the hash works
    const isValid = await bcrypt.compare(newPassword, hashedPassword);
    console.log(`\n✅ Hash Verification: ${isValid ? 'VALID' : 'INVALID'}`);
    
    console.log(`\n📋 Use this hash in storage.ts:`);
    console.log(`password: "${hashedPassword}", // Hashed "eco-grovea#Admin#"`);
    
  } catch (error) {
    console.error('❌ Hash generation failed:', error.message);
  }
}

// Run the generator
generatePasswordHash();

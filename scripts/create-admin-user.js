#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to create an admin user for testing
 */

import { Pool } from '@neondatabase/serverless';
import bcrypt from 'bcrypt';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require'
});

async function createAdminUser() {
  console.log('👤 Creating Admin User...\n');

  try {
    // Check if admin user already exists
    console.log('1. Checking for existing admin user...');
    const existingUser = await pool.query(
      'SELECT id, username, email, role FROM users WHERE username = $1 OR role = $2',
      ['admin', 'admin']
    );

    if (existingUser.rows.length > 0) {
      console.log('   ✅ Admin user already exists:');
      existingUser.rows.forEach(user => {
        console.log(`      - ID: ${user.id}, Username: ${user.username}, Email: ${user.email}, Role: ${user.role}`);
      });
      
      // Update the password for the existing admin user
      console.log('\n2. Updating admin password...');
      const hashedPassword = await bcrypt.hash('admin123', 12);
      
      await pool.query(
        `UPDATE users 
         SET password = $1, 
             password_changed_at = NOW(),
             updated_at = NOW()
         WHERE username = 'admin' OR role = 'admin'`,
        [hashedPassword]
      );
      
      console.log('   ✅ Admin password updated to: admin123');
    } else {
      // Create new admin user
      console.log('   ℹ️  No admin user found. Creating new admin user...');
      
      console.log('\n2. Creating admin user...');
      const hashedPassword = await bcrypt.hash('admin123', 12);
      
      const result = await pool.query(
        `INSERT INTO users (
          username, email, password, first_name, last_name, role,
          is_active, is_email_verified, password_changed_at,
          two_factor_enabled, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), $9, NOW(), NOW())
        RETURNING id, username, email, role`,
        [
          'admin',
          '<EMAIL>',
          hashedPassword,
          'Admin',
          'User',
          'admin',
          true,
          true,
          false
        ]
      );
      
      console.log('   ✅ Admin user created:');
      console.log(`      - ID: ${result.rows[0].id}`);
      console.log(`      - Username: ${result.rows[0].username}`);
      console.log(`      - Email: ${result.rows[0].email}`);
      console.log(`      - Role: ${result.rows[0].role}`);
    }

    // Verify the user can be found
    console.log('\n3. Verifying admin user...');
    const verifyUser = await pool.query(
      'SELECT id, username, email, role, two_factor_enabled FROM users WHERE username = $1',
      ['admin']
    );

    if (verifyUser.rows.length > 0) {
      const user = verifyUser.rows[0];
      console.log('   ✅ Admin user verified:');
      console.log(`      - Username: ${user.username}`);
      console.log(`      - Email: ${user.email}`);
      console.log(`      - Role: ${user.role}`);
      console.log(`      - 2FA Enabled: ${user.two_factor_enabled}`);
    } else {
      throw new Error('Failed to verify admin user creation');
    }

    console.log('\n🎉 Admin user setup completed!');
    console.log('\n📋 Login Credentials:');
    console.log('   Username: admin');
    console.log('   Password: admin123');
    console.log('\n🔐 Security Notes:');
    console.log('   • Change the default password after first login');
    console.log('   • Enable 2FA for enhanced security');
    console.log('   • Monitor security logs regularly');

  } catch (error) {
    console.error('❌ Failed to create admin user:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Verify database connection first
async function verifyConnection() {
  try {
    const result = await pool.query('SELECT 1 as test');
    console.log('✅ Database connection verified');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
}

// Main execution
(async () => {
  console.log('🔒 EcoGrovea Admin User Setup');
  console.log('=============================\n');

  const connected = await verifyConnection();
  if (!connected) {
    console.error('Please check your database connection and try again.');
    process.exit(1);
  }

  await createAdminUser();
})();

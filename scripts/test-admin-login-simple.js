#!/usr/bin/env node

/**
 * Simple test script to verify admin login functionality
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5000';

async function testAdminLogin() {
  console.log('🔐 Testing Admin Login...\n');
  
  try {
    // 1. Get CSRF token
    console.log('1. Getting CSRF token...');
    const csrfResponse = await fetch(`${BASE_URL}/api/auth/csrf-token`, {
      credentials: 'include',
    });
    
    if (!csrfResponse.ok) {
      throw new Error('Failed to get CSRF token');
    }
    
    const { csrfToken } = await csrfResponse.json();
    console.log(`   ✅ CSRF Token received: ${csrfToken.substring(0, 16)}...`);
    
    // Extract session cookie
    const sessionCookie = csrfResponse.headers.get('set-cookie')?.split(';')[0];
    
    // 2. Test admin login
    console.log('\n2. Testing admin login...');
    const loginResponse = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfToken,
        'Cookie': sessionCookie || '',
      },
      credentials: 'include',
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123',
      }),
    });
    
    const loginData = await loginResponse.json();
    console.log(`   Status: ${loginResponse.status}`);
    
    if (loginResponse.ok) {
      console.log('   ✅ SUCCESS: Admin login working!');
      console.log(`   👤 User: ${loginData.user?.username}`);
      console.log(`   🔐 Role: ${loginData.user?.role}`);
      
      if (loginData.requiresTwoFactor) {
        console.log('   📱 2FA Required: YES');
      } else {
        console.log('   📱 2FA Required: NO');
      }
    } else {
      console.log('   ❌ FAILED: Admin login failed');
      console.log(`   Error: ${loginData.message}`);
    }
    
    // 3. Check auth status
    console.log('\n3. Checking auth status...');
    const statusResponse = await fetch(`${BASE_URL}/api/auth/status`, {
      headers: {
        'Cookie': sessionCookie || '',
      },
      credentials: 'include',
    });
    
    const statusData = await statusResponse.json();
    console.log(`   Status: ${statusResponse.status}`);
    console.log(`   Authenticated: ${statusData.authenticated}`);
    console.log(`   Is Admin: ${statusData.isAdmin}`);
    console.log(`   2FA Verified: ${statusData.twoFactorVerified}`);
    
    console.log('\n🎉 Test completed!');
    
    if (loginResponse.ok && statusData.authenticated && statusData.isAdmin) {
      console.log('\n✅ RESULT: Admin login is working correctly!');
      console.log('\n📋 Next Steps:');
      console.log('   1. Open http://localhost:3000/admin in your browser');
      console.log('   2. Login with username: admin, password: admin123');
      console.log('   3. Set up 2FA for enhanced security');
      console.log('   4. Explore the security dashboard');
    } else {
      console.log('\n❌ RESULT: Admin login needs attention');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testAdminLogin();

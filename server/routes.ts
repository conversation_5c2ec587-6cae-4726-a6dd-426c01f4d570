import type { Express, Request, Response, NextFunction } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertCartItemSchema, insertOrderSchema, insertContactSchema, insertProductSchema, insertProductVariationSchema } from "@shared/schema";
import { fromZodError } from "zod-validation-error";
import { upload, getImageUrl, handleUploadError, deleteImageFile, getFilenameFromUrl } from "./upload";
import { uploadMultipleToCloudinary, testCloudinaryConnection, isCloudinaryConfigured, getCloudinaryInfo, deleteFromCloudinary } from "./cloudinary-storage";



// Extend session interface
declare module 'express-session' {
  interface SessionData {
    isAdmin?: boolean;
    userId?: number;
    userRole?: string;
  }
}

// Authentication middleware
function requireAuth(req: Request, res: Response, next: NextFunction) {
  if (req.session?.isAdmin || (req.session?.userId && req.session?.userRole === "admin")) {
    next();
  } else {
    res.status(401).json({ message: "Admin authentication required" });
  }
}

// User authentication middleware
function requireUserAuth(req: Request, res: Response, next: NextFunction) {
  if (req.session?.userId) {
    next();
  } else {
    res.status(401).json({ message: "Authentication required" });
  }
}

// Role-based authentication middleware
function requireRole(role: string) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (req.session?.userRole === role || (role === "admin" && req.session?.isAdmin)) {
      next();
    } else {
      res.status(403).json({ message: `${role} role required` });
    }
  };
}

// Permission-based middleware
function hasPermission(permission: string) {
  return (req: Request, res: Response, next: NextFunction) => {
    const userRole = req.session?.userRole;
    const isAdmin = req.session?.isAdmin;

    const permissions: Record<string, string[]> = {
      // Admin permissions
      "admin.view_dashboard": ["admin"],
      "admin.manage_orders": ["admin"],
      "admin.manage_contacts": ["admin"],
      "admin.manage_products": ["admin"],
      "admin.view_analytics": ["admin"],

      // User permissions
      "user.place_order": ["user", "admin"],
      "user.view_profile": ["user", "admin"],
      "user.contact_support": ["user", "admin"],

      // Public permissions (no auth required)
      "public.view_products": ["guest", "user", "admin"],
      "public.view_content": ["guest", "user", "admin"],
    };

    const allowedRoles = permissions[permission] || [];

    if (isAdmin || allowedRoles.includes(userRole || "guest")) {
      next();
    } else {
      res.status(403).json({
        message: "Insufficient permissions",
        required: permission,
        userRole: userRole || "guest"
      });
    }
  };
}

// API access control middleware
function apiAccessControl(apiType: "public" | "user" | "admin") {
  return (req: Request, res: Response, next: NextFunction) => {
    const userRole = req.session?.userRole;
    const isAdmin = req.session?.isAdmin;

    switch (apiType) {
      case "public":
        // Anyone can access
        next();
        break;

      case "user":
        // Authenticated users only
        if (req.session?.userId || isAdmin) {
          next();
        } else {
          res.status(401).json({ message: "Authentication required" });
        }
        break;

      case "admin":
        // Admin only
        if (isAdmin || userRole === "admin") {
          next();
        } else {
          res.status(403).json({ message: "Admin access required" });
        }
        break;

      default:
        res.status(500).json({ message: "Invalid API access type" });
    }
  };
}

// Admin credentials (in production, use proper user management)
const ADMIN_CREDENTIALS = {
  username: "admin",
  password: "admin123"
};

export async function registerRoutes(app: Express): Promise<Server> {
  // User authentication routes
  app.post("/api/auth/register", async (req, res) => {
    try {
      const { username, email, password, firstName, lastName } = req.body;

      // Check if user already exists
      const existingUser = await storage.getUserByUsername(username) || await storage.getUserByEmail(email);
      if (existingUser) {
        return res.status(400).json({ message: "User already exists" });
      }

      // Create user
      const user = await storage.createUser({ username, email, password, firstName, lastName });

      // Set session
      req.session.userId = user.id;
      req.session.userRole = user.role;

      res.json({
        message: "Registration successful",
        user: { ...user, password: undefined } // Don't send password
      });
    } catch (error) {
      res.status(500).json({ message: "Registration failed" });
    }
  });

  app.post("/api/auth/login", async (req, res) => {
    try {
      const { username, password } = req.body;

      const user = await storage.getUserByUsername(username);
      if (!user) {
        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Check if user is locked
      if (user.lockedUntil && user.lockedUntil > new Date()) {
        return res.status(423).json({ message: "Account is temporarily locked" });
      }

      // Check password (in production, use proper password hashing)
      if (user.password !== password) {
        await storage.incrementLoginAttempts(user.id);

        // Lock user after 5 failed attempts
        if (user.loginAttempts >= 4) {
          const lockUntil = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
          await storage.lockUser(user.id, lockUntil);
          return res.status(423).json({ message: "Account locked due to too many failed attempts" });
        }

        return res.status(401).json({ message: "Invalid credentials" });
      }

      // Reset login attempts on successful login
      await storage.resetLoginAttempts(user.id);
      await storage.updateUser(user.id, { lastLogin: new Date() });

      // Set session
      req.session.userId = user.id;
      req.session.userRole = user.role;

      res.json({
        message: "Login successful",
        user: { ...user, password: undefined }
      });
    } catch (error) {
      res.status(500).json({ message: "Login failed" });
    }
  });

  app.post("/api/auth/logout", async (req, res) => {
    try {
      req.session.destroy((err) => {
        if (err) {
          return res.status(500).json({ message: "Logout failed" });
        }
        res.clearCookie('connect.sid');
        res.json({ message: "Logout successful" });
      });
    } catch (error) {
      res.status(500).json({ message: "Logout failed" });
    }
  });

  app.get("/api/auth/status", async (req, res) => {
    try {
      if (req.session?.userId) {
        const user = await storage.getUserById(req.session.userId);
        if (user) {
          return res.json({
            authenticated: true,
            user: { ...user, password: undefined }
          });
        }
      }
      res.json({ authenticated: false, user: null });
    } catch (error) {
      res.status(500).json({ message: "Failed to check auth status" });
    }
  });

  // Products (Public API)
  app.get("/api/products", apiAccessControl("public"), async (req, res) => {
    try {
      const { category, search, featured } = req.query;

      let products;
      if (search) {
        products = await storage.searchProducts(search as string);
      } else if (category && category !== "all") {
        products = await storage.getProductsByCategory(category as string);
      } else if (featured === "true") {
        products = await storage.getFeaturedProducts();
      } else {
        products = await storage.getProducts();
      }

      res.json(products);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch products" });
    }
  });

  app.get("/api/products/:id", apiAccessControl("public"), async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const product = await storage.getProduct(id);

      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }

      res.json(product);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch product" });
    }
  });

  // Product variations (Public API)
  app.get("/api/products/:id/variations", apiAccessControl("public"), async (req, res) => {
    try {
      const productId = parseInt(req.params.id);
      const variations = await storage.getProductVariations(productId);
      res.json(variations);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch product variations" });
    }
  });

  // Cart management (Public API - available to all users)
  app.get("/api/cart", apiAccessControl("public"), async (req, res) => {
    try {
      const sessionId = (req as any).sessionID || "default-session";
      const cartItems = await storage.getCartItems(sessionId);
      res.json(cartItems);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch cart items" });
    }
  });

  app.post("/api/cart", apiAccessControl("public"), async (req, res) => {
    try {
      const sessionId = (req as any).sessionID || "default-session";
      const validatedData = insertCartItemSchema.parse({
        ...req.body,
        sessionId
      });
      
      const cartItem = await storage.addToCart(validatedData);
      res.json(cartItem);
    } catch (error: any) {
      if (error.name === "ZodError") {
        const validationError = fromZodError(error);
        return res.status(400).json({ message: validationError.toString() });
      }
      res.status(500).json({ message: "Failed to add item to cart" });
    }
  });

  app.put("/api/cart/:productId", apiAccessControl("public"), async (req, res) => {
    try {
      const sessionId = (req as any).sessionID || "default-session";
      const productId = parseInt(req.params.productId);
      const { quantity } = req.body;

      if (!quantity || quantity < 0) {
        return res.status(400).json({ message: "Invalid quantity" });
      }

      const updatedItem = await storage.updateCartItem(sessionId, productId, quantity);
      res.json(updatedItem);
    } catch (error) {
      res.status(500).json({ message: "Failed to update cart item" });
    }
  });

  // Update specific variation in cart
  app.put("/api/cart/:productId/:variationId", apiAccessControl("public"), async (req, res) => {
    try {
      const sessionId = (req as any).sessionID || "default-session";
      const productId = parseInt(req.params.productId);
      const variationId = parseInt(req.params.variationId);
      const { quantity } = req.body;

      if (!quantity || quantity < 0) {
        return res.status(400).json({ message: "Invalid quantity" });
      }

      const updatedItem = await storage.updateCartItem(sessionId, productId, quantity, variationId);
      res.json(updatedItem);
    } catch (error) {
      res.status(500).json({ message: "Failed to update cart item" });
    }
  });

  app.delete("/api/cart/:productId", apiAccessControl("public"), async (req, res) => {
    try {
      const sessionId = (req as any).sessionID || "default-session";
      const productId = parseInt(req.params.productId);

      await storage.removeFromCart(sessionId, productId);
      res.json({ message: "Item removed from cart" });
    } catch (error) {
      res.status(500).json({ message: "Failed to remove item from cart" });
    }
  });

  // Remove specific variation from cart
  app.delete("/api/cart/:productId/:variationId", apiAccessControl("public"), async (req, res) => {
    try {
      const sessionId = (req as any).sessionID || "default-session";
      const productId = parseInt(req.params.productId);
      const variationId = parseInt(req.params.variationId);

      await storage.removeFromCart(sessionId, productId, variationId);
      res.json({ message: "Item removed from cart" });
    } catch (error) {
      res.status(500).json({ message: "Failed to remove item from cart" });
    }
  });

  app.delete("/api/cart", apiAccessControl("public"), async (req, res) => {
    try {
      const sessionId = (req as any).sessionID || "default-session";
      await storage.clearCart(sessionId);
      res.json({ message: "Cart cleared" });
    } catch (error) {
      res.status(500).json({ message: "Failed to clear cart" });
    }
  });

  // Orders/Enquiries (Public API - no authentication required)
  app.post("/api/orders", apiAccessControl("public"), async (req, res) => {
    try {
      // Handle both old format (with shippingAddress) and new format (with deliveryLocation)
      const orderData = req.body;

      // Debug: Log the incoming order data
      console.log("📦 Incoming order data:", JSON.stringify(orderData, null, 2));

      // Convert new enquiry format to order format for database compatibility
      if (orderData.deliveryLocation && !orderData.shippingAddress) {
        orderData.shippingAddress = {
          street: orderData.deliveryLocation,
          city: "To be confirmed",
          state: "To be confirmed",
          zipCode: "000000",
          country: "India"
        };
      }

      // Set default values for enquiry system
      if (!orderData.tax) {
        orderData.tax = "0.00";
      }

      // Add session information for tracking
      const sessionId = (req as any).sessionID || "default-session";
      if (!orderData.sessionId && orderData.isGuest) {
        orderData.sessionId = sessionId;
      }

      console.log("🔍 Order data before validation:", JSON.stringify(orderData, null, 2));

      const validatedData = insertOrderSchema.parse(orderData);
      console.log("✅ Validation successful, creating order...");

      const order = await storage.createOrder(validatedData);

      // Clear cart after successful order
      await storage.clearCart(sessionId);

      res.status(201).json(order);
    } catch (error: any) {
      console.error("❌ Order creation error:", error);
      if (error.name === "ZodError") {
        console.error("🔍 Zod validation errors:", error.errors);
        const validationError = fromZodError(error);
        return res.status(400).json({ message: validationError.toString() });
      }
      res.status(500).json({ message: "Failed to create order enquiry" });
    }
  });

  app.get("/api/orders/:id", apiAccessControl("user"), async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const order = await storage.getOrder(id);

      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      res.json(order);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch order" });
    }
  });

  // Contact form (Public API)
  app.post("/api/contact", apiAccessControl("public"), async (req, res) => {
    try {
      const validatedData = insertContactSchema.parse(req.body);
      const contact = await storage.createContact(validatedData);
      res.status(201).json({ message: "Contact form submitted successfully", id: contact.id });
    } catch (error: any) {
      if (error.name === "ZodError") {
        const validationError = fromZodError(error);
        return res.status(400).json({ message: validationError.toString() });
      }
      res.status(500).json({ message: "Failed to submit contact form" });
    }
  });

  // Admin authentication routes (legacy support)
  app.post("/api/admin/auth/login", async (req, res) => {
    try {
      const { username, password } = req.body;

      // Try to authenticate with user system first
      const user = await storage.getUserByUsername(username);
      if (user && user.password === password && user.role === "admin") {
        req.session.userId = user.id;
        req.session.userRole = user.role;
        req.session.isAdmin = true;
        await storage.updateUser(user.id, { lastLogin: new Date() });
        return res.json({
          message: "Login successful",
          authenticated: true,
          user: { ...user, password: undefined }
        });
      }

      // Fallback to hardcoded admin credentials (deprecated - should be removed in production)
      if (sanitizedUsername === ADMIN_CREDENTIALS.username) {
        const isPasswordValid = await verifyPassword(password, ADMIN_CREDENTIALS.passwordHash);
        if (isPasswordValid) {
          securityManager.clearLoginAttempts(clientIP, sanitizedUsername);
          req.session.isAdmin = true;

          SecurityLogger.logSecurityEvent({
            type: 'ADMIN_LEGACY_LOGIN' as any,
            username: sanitizedUsername,
            ip: clientIP,
            userAgent,
            details: { warning: 'Using deprecated hardcoded credentials' },
            timestamp: new Date(),
          });

          res.json({ message: "Login successful", authenticated: true });
        } else {
          securityManager.recordLoginAttempt(clientIP, sanitizedUsername, false);
          SecurityLogger.logLoginFailure(sanitizedUsername, clientIP, 'Invalid admin credentials', userAgent);
          res.status(401).json({ message: "Invalid credentials" });
        }
      } else {
        securityManager.recordLoginAttempt(clientIP, sanitizedUsername, false);
        SecurityLogger.logLoginFailure(sanitizedUsername, clientIP, 'Invalid admin credentials', userAgent);
        res.status(401).json({ message: "Invalid credentials" });
      }
    } catch (error) {
      res.status(500).json({ message: "Login failed" });
    }
  });

  app.post("/api/admin/auth/logout", async (req, res) => {
    try {
      req.session.destroy((err) => {
        if (err) {
          return res.status(500).json({ message: "Logout failed" });
        }
        res.clearCookie('connect.sid');
        res.json({ message: "Logout successful", authenticated: false });
      });
    } catch (error) {
      res.status(500).json({ message: "Logout failed" });
    }
  });

  app.get("/api/admin/auth/status", async (req, res) => {
    try {
      if (req.session?.userId) {
        const user = await storage.getUserById(req.session.userId);
        if (user && user.role === "admin") {
          return res.json({
            authenticated: true,
            user: { ...user, password: undefined }
          });
        }
      }
      res.json({ authenticated: !!req.session?.isAdmin });
    } catch (error) {
      res.status(500).json({ message: "Failed to check auth status" });
    }
  });

  // Admin routes (Admin API - requires admin role)
  app.get("/api/admin/contacts", apiAccessControl("admin"), async (req, res) => {
    try {
      const contacts = await storage.getContacts();
      res.json(contacts);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch contacts" });
    }
  });

  app.get("/api/admin/orders", apiAccessControl("admin"), async (req, res) => {
    try {
      const orders = await storage.getOrders();
      res.json(orders);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch orders" });
    }
  });

  app.get("/api/admin/products", apiAccessControl("admin"), async (req, res) => {
    try {
      const products = await storage.getProducts();
      res.json(products);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch products" });
    }
  });



  // Cloudinary status endpoint
  app.get("/api/admin/cloudinary-status", apiAccessControl("admin"), async (req, res) => {
    try {
      const cloudinaryInfo = getCloudinaryInfo();
      const connectionTest = await testCloudinaryConnection();

      res.json({
        ...cloudinaryInfo,
        connectionTest,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        message: "Failed to check Cloudinary status",
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Upload product images
  app.post("/api/admin/upload-images", apiAccessControl("admin"), upload.array('images', 3), handleUploadError, async (req: Request, res: Response) => {
    try {
      const files = req.files as Express.Multer.File[];
      if (!files || files.length === 0) {
        return res.status(400).json({ message: "No images uploaded" });
      }

      // Check if Cloudinary is configured and enabled
      if (isCloudinaryConfigured()) {
        try {
          console.log(`☁️  Uploading ${files.length} images to Cloudinary...`);

          // Upload to Cloudinary
          const uploadResults = await uploadMultipleToCloudinary(files, 'products');
          const imageUrls = uploadResults.map(result => result.publicUrl);

          console.log(`✅ Successfully uploaded ${imageUrls.length} images to Cloudinary`);

          res.json({
            message: `Images uploaded successfully to Cloudinary`,
            imageUrls,
            primaryImage: imageUrls[0],
            storage: "cloudinary",
            count: imageUrls.length,
            cloudinaryInfo: getCloudinaryInfo()
          });
        } catch (cloudinaryError) {
          console.error('❌ Cloudinary upload failed:', cloudinaryError);
          console.log('🔄 Falling back to local storage...');

          // Fall back to local storage
          const imageUrls = files.map(file => getImageUrl(file.filename));
          res.json({
            message: "Images uploaded to local storage (Cloudinary fallback)",
            imageUrls,
            primaryImage: imageUrls[0],
            storage: "local",
            count: imageUrls.length,
            error: "Cloudinary upload failed, used local fallback"
          });
        }
      } else {
        console.log('🔧 Cloudinary not configured, using local storage');

        // Use local storage
        const imageUrls = files.map(file => getImageUrl(file.filename));
        res.json({
          message: "Images uploaded successfully to local storage",
          imageUrls,
          primaryImage: imageUrls[0],
          storage: "local",
          count: imageUrls.length,
          info: "Configure Cloudinary to enable cloud storage with CDN"
        });
      }
    } catch (error) {
      console.error('Upload error:', error);
      res.status(500).json({ message: "Failed to upload images" });
    }
  });

  // Delete image from cloud storage
  app.delete("/api/admin/delete-image", apiAccessControl("admin"), async (req, res) => {
    try {
      const { imageUrl } = req.body;

      if (!imageUrl) {
        return res.status(400).json({ message: "Image URL is required" });
      }

      // Extract public ID from Cloudinary URL
      const publicId = extractPublicIdFromUrl(imageUrl);

      if (!publicId) {
        return res.status(400).json({ message: "Invalid Cloudinary URL" });
      }

      // Check if Cloudinary is configured
      if (isCloudinaryConfigured()) {
        try {
          await deleteFromCloudinary(publicId);
          res.json({
            message: "Image deleted successfully from Cloudinary",
            imageUrl,
            publicId
          });
        } catch (cloudinaryError) {
          console.error('❌ Cloudinary delete failed:', cloudinaryError);
          res.status(500).json({
            message: "Failed to delete image from Cloudinary",
            error: cloudinaryError instanceof Error ? cloudinaryError.message : 'Unknown error'
          });
        }
      } else {
        // For local storage, try to delete the file
        try {
          const filename = getFilenameFromUrl(imageUrl);
          deleteImageFile(filename);
          res.json({
            message: "Image deleted successfully from local storage",
            imageUrl,
            filename
          });
        } catch (localError) {
          console.error('❌ Local delete failed:', localError);
          res.status(500).json({
            message: "Failed to delete image from local storage",
            error: localError instanceof Error ? localError.message : 'Unknown error'
          });
        }
      }
    } catch (error) {
      console.error('Delete image error:', error);
      res.status(500).json({ message: "Failed to delete image" });
    }
  });

  // Helper function to extract public ID from Cloudinary URL
  function extractPublicIdFromUrl(url: string): string | null {
    try {
      // Cloudinary URL format: https://res.cloudinary.com/{cloud_name}/image/upload/{transformations}/{public_id}.{format}
      const match = url.match(/\/eco-dinnerware\/[^\/]+\/([^\.]+)/);
      if (match) {
        return `eco-dinnerware/products/${match[1]}`;
      }

      // Alternative pattern for different URL structures
      const altMatch = url.match(/\/eco-dinnerware\/products\/([^\.]+)/);
      if (altMatch) {
        return `eco-dinnerware/products/${altMatch[1]}`;
      }

      return null;
    } catch (error) {
      console.error('Error extracting public ID:', error);
      return null;
    }
  }

  // Create product
  app.post("/api/admin/products", apiAccessControl("admin"), async (req, res) => {
    try {
      console.log('📦 Creating product with data:', JSON.stringify(req.body, null, 2));

      const { variations, ...productData } = req.body;

      const result = insertProductSchema.safeParse(productData);
      if (!result.success) {
        console.error('❌ Product validation failed:', result.error);
        return res.status(400).json({
          message: "Invalid product data",
          errors: fromZodError(result.error).toString()
        });
      }

      console.log('✅ Product data validated, creating product...');
      const product = await storage.createProduct(result.data);
      console.log('✅ Product created:', product.id);

      // Create variations if provided
      if (variations && Array.isArray(variations) && variations.length > 0) {
        console.log('🔧 Creating variations:', variations.length);
        for (const variation of variations) {
          if (variation.name && variation.value) {
            try {
              console.log('🔍 Processing variation from frontend:', JSON.stringify(variation, null, 2));
              const variationData = {
                productId: product.id,
                name: variation.name,
                type: variation.type || 'size',
                value: variation.value,
                price: variation.priceAdjustment ? (parseFloat(product.price) + variation.priceAdjustment).toString() : null,
                stockQuantity: variation.stockQuantity || 0,
                isActive: variation.isActive !== false,
                sortOrder: 0,
                // Carton information (optional, falls back to product defaults)
                unitsPerCarton: variation.unitsPerCarton || null,
                unitType: variation.unitType || null,
                cartonWeight: variation.cartonWeight || null,
                cartonDimensions: variation.cartonDimensions || null,
              };
              console.log('🔧 Creating variation with data:', JSON.stringify(variationData, null, 2));
              await storage.createProductVariation(variationData);
            } catch (variationError) {
              console.error('❌ Variation creation failed:', variationError);
              // Continue with other variations even if one fails
            }
          }
        }
      }

      res.status(201).json(product);
    } catch (error) {
      console.error('❌ Product creation error:', error);
      res.status(500).json({ message: "Failed to create product", error: error.message });
    }
  });

  // Update product
  app.patch("/api/admin/products/:id", apiAccessControl("admin"), async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { variations, ...productData } = req.body;

      console.log('📝 Updating product:', id);
      console.log('📝 Request body keys:', Object.keys(req.body));
      console.log('📝 Product data:', JSON.stringify(productData, null, 2));
      console.log('🔧 Variations field exists:', 'variations' in req.body);
      console.log('🔧 Variations value:', variations);
      console.log('🔧 Variations type:', typeof variations);
      console.log('🔧 Variations is array:', Array.isArray(variations));

      const product = await storage.updateProduct(id, productData);
      if (!product) {
        return res.status(404).json({ message: "Product not found" });
      }

      // Handle variations update if provided
      if (variations && Array.isArray(variations)) {
        console.log('🔧 Updating variations for product:', id);
        console.log('🔧 Number of variations to process:', variations.length);
        console.log('🔧 Variations data:', JSON.stringify(variations, null, 2));

        // For now, we'll delete existing variations and create new ones
        // In a more sophisticated implementation, you'd update existing ones
        try {
          // Get existing variations
          const existingVariations = await storage.getProductVariations(id);
          console.log('🔧 Found existing variations:', existingVariations.length);

          // Delete existing variations (if any)
          for (const existingVar of existingVariations) {
            if (existingVar.id) {
              console.log('🗑️ Deleting existing variation:', existingVar.id, existingVar.name);
              await storage.deleteProductVariation(existingVar.id);
            }
          }

          console.log('✅ All existing variations deleted');

          // Create new variations
          let createdCount = 0;
          for (const variation of variations) {
            if (variation.name && variation.value) {
              console.log('🔍 Processing variation for update from frontend:', JSON.stringify(variation, null, 2));
              const variationData = {
                productId: id,
                name: variation.name,
                type: variation.type || 'size',
                value: variation.value,
                price: variation.priceAdjustment ? (parseFloat(product.price) + variation.priceAdjustment).toString() : null,
                stockQuantity: variation.stockQuantity || 0,
                isActive: variation.isActive !== false,
                sortOrder: createdCount,
                // Carton information (optional, falls back to product defaults)
                unitsPerCarton: variation.unitsPerCarton || null,
                unitType: variation.unitType || null,
                cartonWeight: variation.cartonWeight || null,
                cartonDimensions: variation.cartonDimensions || null,
              };
              console.log('🔧 Creating updated variation with data:', JSON.stringify(variationData, null, 2));
              const createdVariation = await storage.createProductVariation(variationData);
              console.log('✅ Created variation with ID:', createdVariation.id);
              createdCount++;
            } else {
              console.log('⚠️ Skipping invalid variation:', variation);
            }
          }

          console.log('✅ Created', createdCount, 'new variations');
        } catch (variationError) {
          console.error('❌ Variation update failed:', variationError);
          // Continue even if variations fail
        }
      } else {
        console.log('🔧 No variations provided or variations is not an array');
      }

      res.json(product);
    } catch (error) {
      console.error('❌ Product update error:', error);
      res.status(500).json({ message: "Failed to update product", error: error.message });
    }
  });

  // Delete product
  app.delete("/api/admin/products/:id", apiAccessControl("admin"), async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteProduct(id);
      res.json({ message: "Product deleted successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete product" });
    }
  });

  // Mark order as seen/unseen
  app.patch("/api/admin/orders/:id/seen", apiAccessControl("admin"), async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { seen } = req.body;

      if (typeof seen !== 'boolean') {
        return res.status(400).json({ message: "Seen must be a boolean value" });
      }

      const order = await storage.markOrderAsSeen(id, seen);
      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      res.json(order);
    } catch (error) {
      res.status(500).json({ message: "Failed to update order" });
    }
  });

  // Update order status
  app.patch("/api/admin/orders/:id/status", apiAccessControl("admin"), async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { status } = req.body;

      // Validate status
      const validStatuses = ["pending", "confirmed", "processing", "shipped", "delivered", "cancelled"];
      if (!status || !validStatuses.includes(status)) {
        return res.status(400).json({
          message: "Invalid status. Must be one of: " + validStatuses.join(", ")
        });
      }

      const order = await storage.updateOrderStatus(id, status);
      if (!order) {
        return res.status(404).json({ message: "Order not found" });
      }

      res.json(order);
    } catch (error) {
      res.status(500).json({ message: "Failed to update order status" });
    }
  });

  // Delete order
  app.delete("/api/admin/orders/:id", apiAccessControl("admin"), async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteOrder(id);
      res.json({ message: "Order deleted successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete order" });
    }
  });

  // Mark contact as seen/unseen
  app.patch("/api/admin/contacts/:id/seen", apiAccessControl("admin"), async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const { seen } = req.body;

      if (typeof seen !== 'boolean') {
        return res.status(400).json({ message: "Seen must be a boolean value" });
      }

      const contact = await storage.markContactAsSeen(id, seen);
      if (!contact) {
        return res.status(404).json({ message: "Contact not found" });
      }

      res.json(contact);
    } catch (error) {
      res.status(500).json({ message: "Failed to update contact" });
    }
  });

  // Delete contact
  app.delete("/api/admin/contacts/:id", apiAccessControl("admin"), async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      await storage.deleteContact(id);
      res.json({ message: "Contact deleted successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete contact" });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}

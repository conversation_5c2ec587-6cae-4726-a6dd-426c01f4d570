import { drizzle } from "drizzle-orm/neon-http";
import { neon } from "@neondatabase/serverless";
import * as dotenv from "dotenv";
import * as schema from "@shared/schema";

dotenv.config();

if (!process.env.DATABASE_URL) {
  throw new Error("DATABASE_URL environment variable is required");
}

// Create the connection using HTTP driver
const sql = neon(process.env.DATABASE_URL);

// Create the drizzle instance
export const db = drizzle(sql, {
  schema,
  logger: process.env.NODE_ENV === "development"
});

// Export the connection for raw queries if needed
export { sql };

// Test connection function
export async function testConnection() {
  try {
    const result = await sql`SELECT 1 as test`;
    console.log("✅ Database connection successful:", result);
    return true;
  } catch (error) {
    console.error("❌ Database connection failed:", error);
    return false;
  }
}

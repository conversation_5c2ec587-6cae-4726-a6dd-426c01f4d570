import { v2 as cloudinary } from 'cloudinary';

// Cloudinary configuration
const initializeCloudinary = () => {
  const requiredEnvVars = ['CLOUDINARY_CLOUD_NAME', 'CLOUDINARY_API_KEY', 'CLOUDINARY_API_SECRET'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.warn(`⚠️  Cloudinary configuration incomplete. Missing: ${missingVars.join(', ')}`);
    console.warn('📝 Add these to your .env file to enable Cloudinary storage');
    return false;
  }

  cloudinary.config({
    cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
    api_key: process.env.CLOUDINARY_API_KEY,
    api_secret: process.env.CLOUDINARY_API_SECRET,
  });
  return true;
};

// Initialize on module load
const isCloudinaryEnabled = initializeCloudinary();

export interface UploadResult {
  publicUrl: string;
  publicId: string;
  fileName: string;
  originalName: string;
  width: number;
  height: number;
  format: string;
  bytes: number;
}

/**
 * Upload a file to Cloudinary
 */
export async function uploadToCloudinary(
  file: Express.Multer.File,
  folder: string = 'products'
): Promise<UploadResult> {
  try {
    if (!isCloudinaryEnabled) {
      throw new Error('Cloudinary not configured');
    }

    // Check if file buffer exists
    if (!file.buffer) {
      throw new Error('File buffer is missing');
    }

    // Upload to Cloudinary using buffer directly
    const result = await new Promise<any>((resolve, reject) => {
      cloudinary.uploader.upload_stream(
        {
          folder: `eco-dinnerware/${folder}`,
          public_id: `${Date.now()}-${Math.round(Math.random() * 1E9)}`,
          resource_type: 'image',
          transformation: [
            { width: 800, height: 800, crop: 'limit', quality: 'auto:good' },
            { fetch_format: 'auto' }
          ],
          tags: ['product', 'eco-dinnerware']
        },
        (error, result) => {
          if (error) {
            reject(error);
          } else {
            resolve(result);
          }
        }
      ).end(file.buffer);
    });

    console.log(`✅ File uploaded to Cloudinary: ${result.public_id}`);

    return {
      publicUrl: result.secure_url,
      publicId: result.public_id,
      fileName: result.public_id,
      originalName: file.originalname,
      width: result.width,
      height: result.height,
      format: result.format,
      bytes: result.bytes,
    };
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    throw new Error('Failed to upload to Cloudinary');
  }
}

/**
 * Upload multiple files to Cloudinary
 */
export async function uploadMultipleToCloudinary(
  files: Express.Multer.File[],
  folder: string = 'products'
): Promise<UploadResult[]> {
  const uploadPromises = files.map(file => uploadToCloudinary(file, folder));
  return Promise.all(uploadPromises);
}

/**
 * Delete a file from Cloudinary
 */
export async function deleteFromCloudinary(publicId: string): Promise<void> {
  try {
    if (!isCloudinaryEnabled) {
      throw new Error('Cloudinary not configured');
    }

    const result = await cloudinary.uploader.destroy(publicId);
    
    if (result.result === 'ok') {
      console.log(`✅ File deleted from Cloudinary: ${publicId}`);
    } else {
      console.warn(`⚠️  File deletion result: ${result.result} for ${publicId}`);
    }
  } catch (error) {
    console.error('Error deleting file from Cloudinary:', error);
    throw new Error('Failed to delete file from Cloudinary');
  }
}

/**
 * Test Cloudinary connection
 */
export async function testCloudinaryConnection(): Promise<boolean> {
  try {
    if (!isCloudinaryEnabled) {
      console.warn('Cloudinary not configured');
      return false;
    }

    // Test connection by getting account details
    const result = await cloudinary.api.ping();
    
    if (result.status === 'ok') {
      console.log('✅ Cloudinary connection successful');
      return true;
    } else {
      console.error('❌ Cloudinary connection failed:', result);
      return false;
    }
  } catch (error) {
    console.error('❌ Cloudinary connection test failed:', error);
    return false;
  }
}

/**
 * Get Cloudinary configuration info
 */
export function getCloudinaryInfo() {
  return {
    configured: isCloudinaryEnabled,
    cloudName: process.env.CLOUDINARY_CLOUD_NAME || 'Not configured',
    status: isCloudinaryEnabled ? 'Ready' : 'Disabled',
    features: [
      '✅ Automatic image optimization',
      '✅ Global CDN delivery',
      '✅ Real-time transformations',
      '✅ Format conversion (WebP, AVIF)',
      '✅ Responsive image delivery',
      '✅ Advanced image analytics'
    ]
  };
}

/**
 * Check if Cloudinary is configured
 */
export function isCloudinaryConfigured(): boolean {
  return isCloudinaryEnabled;
}

/**
 * Get public ID from Cloudinary URL
 */
export function getPublicIdFromUrl(url: string): string {
  try {
    // Extract public ID from Cloudinary URL
    const matches = url.match(/\/v\d+\/(.+)\.[a-zA-Z]+$/);
    return matches ? matches[1] : '';
  } catch (error) {
    console.error('Error parsing Cloudinary URL:', error);
    return '';
  }
}

/**
 * Check if URL is a Cloudinary URL
 */
export function isCloudinaryUrl(url: string): boolean {
  return url.includes('cloudinary.com') || url.includes('res.cloudinary.com');
}

/**
 * Get Cloudinary usage info
 */
export function getCloudinaryUsageInfo() {
  return {
    freeTier: {
      storage: '25GB',
      bandwidth: '25GB/month',
      transformations: '25,000/month',
      images: 'Unlimited'
    },
    benefits: [
      '🚀 Automatic image optimization',
      '🌍 Global CDN (200+ locations)',
      '📱 Responsive image delivery',
      '🎨 Real-time image transformations',
      '📊 Advanced analytics and insights',
      '🔒 Secure image delivery'
    ],
    perfectFor: [
      'E-commerce product images',
      'Automatic format optimization',
      'Mobile-responsive delivery',
      'SEO-optimized images'
    ]
  };
}

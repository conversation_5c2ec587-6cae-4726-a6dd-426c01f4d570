import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import bcrypt from 'bcrypt';
import crypto from 'crypto';
import { Request } from 'express';

// Security configuration
export const SECURITY_CONFIG = {
  // Password requirements
  PASSWORD_MIN_LENGTH: 12,
  PASSWORD_REQUIRE_UPPERCASE: true,
  PASSWORD_REQUIRE_LOWERCASE: true,
  PASSWORD_REQUIRE_NUMBERS: true,
  PASSWORD_REQUIRE_SYMBOLS: true,
  
  // Account lockout
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 30 * 60 * 1000, // 30 minutes
  
  // Session security
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
  ADMIN_SESSION_TIMEOUT: 4 * 60 * 60 * 1000, // 4 hours for admin
  
  // 2FA
  TWO_FA_WINDOW: 2, // Allow 2 time steps before/after current
  BACKUP_CODES_COUNT: 10,
  
  // Rate limiting
  RATE_LIMIT_WINDOW: 15 * 60 * 1000, // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: 100,
  ADMIN_RATE_LIMIT_MAX_REQUESTS: 50,
};

// Password utilities
export class PasswordUtils {
  static async hash(password: string): Promise<string> {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  static async verify(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  static validateStrength(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < SECURITY_CONFIG.PASSWORD_MIN_LENGTH) {
      errors.push(`Password must be at least ${SECURITY_CONFIG.PASSWORD_MIN_LENGTH} characters long`);
    }

    if (SECURITY_CONFIG.PASSWORD_REQUIRE_UPPERCASE && !/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (SECURITY_CONFIG.PASSWORD_REQUIRE_LOWERCASE && !/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (SECURITY_CONFIG.PASSWORD_REQUIRE_NUMBERS && !/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (SECURITY_CONFIG.PASSWORD_REQUIRE_SYMBOLS && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

// Two-Factor Authentication utilities
export class TwoFactorAuth {
  static generateSecret(username: string, serviceName: string = 'EcoGrovea Admin'): {
    secret: string;
    qrCodeUrl: string;
    manualEntryKey: string;
  } {
    const secret = speakeasy.generateSecret({
      name: `${serviceName} (${username})`,
      issuer: serviceName,
      length: 32,
    });

    return {
      secret: secret.base32!,
      qrCodeUrl: secret.otpauth_url!,
      manualEntryKey: secret.base32!,
    };
  }

  static async generateQRCode(otpauthUrl: string): Promise<string> {
    return QRCode.toDataURL(otpauthUrl);
  }

  static verifyToken(secret: string, token: string): boolean {
    return speakeasy.totp.verify({
      secret,
      encoding: 'base32',
      token,
      window: SECURITY_CONFIG.TWO_FA_WINDOW,
    });
  }

  static generateBackupCodes(): string[] {
    const codes: string[] = [];
    for (let i = 0; i < SECURITY_CONFIG.BACKUP_CODES_COUNT; i++) {
      // Generate 8-character alphanumeric codes
      const code = crypto.randomBytes(4).toString('hex').toUpperCase();
      codes.push(code);
    }
    return codes;
  }

  static verifyBackupCode(backupCodes: string[], inputCode: string): boolean {
    return backupCodes.includes(inputCode.toUpperCase());
  }

  static removeUsedBackupCode(backupCodes: string[], usedCode: string): string[] {
    return backupCodes.filter(code => code !== usedCode.toUpperCase());
  }
}

// Security logging and monitoring
export class SecurityLogger {
  static async logSecurityEvent(event: {
    userId?: number;
    eventType: string;
    ipAddress: string;
    userAgent?: string;
    details?: Record<string, any>;
    severity?: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  }) {
    // In a real implementation, this would write to the security_logs table
    console.log('🔒 Security Event:', {
      timestamp: new Date().toISOString(),
      ...event,
    });
  }

  static async logLoginAttempt(username: string, ipAddress: string, success: boolean, userAgent?: string) {
    await this.logSecurityEvent({
      eventType: success ? 'LOGIN_SUCCESS' : 'LOGIN_FAILED',
      ipAddress,
      userAgent,
      details: { username, success },
      severity: success ? 'LOW' : 'MEDIUM',
    });
  }

  static async log2FAEvent(userId: number, eventType: '2FA_ENABLED' | '2FA_DISABLED' | '2FA_VERIFIED' | '2FA_FAILED', ipAddress: string) {
    await this.logSecurityEvent({
      userId,
      eventType,
      ipAddress,
      severity: eventType.includes('FAILED') ? 'HIGH' : 'MEDIUM',
    });
  }
}

// Request security utilities
export class RequestSecurity {
  static getClientIP(req: Request): string {
    return (
      (req.headers['x-forwarded-for'] as string)?.split(',')[0] ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      'unknown'
    );
  }

  static getUserAgent(req: Request): string {
    return req.headers['user-agent'] || 'unknown';
  }

  static sanitizeInput(input: string): string {
    // Remove potentially dangerous characters
    return input.replace(/[<>\"'%;()&+]/g, '');
  }

  static validateCSRF(req: Request): boolean {
    // Basic CSRF protection - in production, use a proper CSRF library
    const token = req.headers['x-csrf-token'] || req.body._csrf;
    const sessionToken = req.session?.csrfToken;
    return token === sessionToken;
  }
}

// Rate limiting utilities
export class RateLimiter {
  private static attempts = new Map<string, { count: number; resetTime: number }>();

  static isRateLimited(identifier: string, maxAttempts: number = SECURITY_CONFIG.RATE_LIMIT_MAX_REQUESTS): boolean {
    const now = Date.now();
    const record = this.attempts.get(identifier);

    if (!record || now > record.resetTime) {
      this.attempts.set(identifier, {
        count: 1,
        resetTime: now + SECURITY_CONFIG.RATE_LIMIT_WINDOW,
      });
      return false;
    }

    if (record.count >= maxAttempts) {
      return true;
    }

    record.count++;
    return false;
  }

  static getRemainingAttempts(identifier: string, maxAttempts: number = SECURITY_CONFIG.RATE_LIMIT_MAX_REQUESTS): number {
    const record = this.attempts.get(identifier);
    if (!record || Date.now() > record.resetTime) {
      return maxAttempts;
    }
    return Math.max(0, maxAttempts - record.count);
  }

  static reset(identifier: string): void {
    this.attempts.delete(identifier);
  }
}

// Input validation utilities
export class InputValidator {
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static isValidUsername(username: string): boolean {
    // Username: 3-30 characters, alphanumeric and underscore only
    const usernameRegex = /^[a-zA-Z0-9_]{3,30}$/;
    return usernameRegex.test(username);
  }

  static sanitizeHtml(input: string): string {
    return input
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;');
  }
}

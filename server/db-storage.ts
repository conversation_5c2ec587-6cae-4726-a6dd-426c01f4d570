import { eq, like, desc, and, lt, sql, isNull } from "drizzle-orm";
import { PasswordUtils } from "./security";
import { db } from "./db";
import {
  products,
  productVariations,
  cartItems,
  orders,
  contacts,
  users,
  userSessions,
  securityLogs,
  type Product,
  type InsertProduct,
  type ProductVariation,
  type InsertProductVariation,
  type CartItem,
  type InsertCartItem,
  type Order,
  type InsertOrder,
  type Contact,
  type InsertContact,
  type User,
  type InsertUser,
  type UserSession,
  type UserRegistration
} from "@shared/schema";
import type { IStorage } from "./storage";

export class DatabaseStorage implements IStorage {
  constructor() {
    // Initialize data after a short delay to ensure database is ready
    setTimeout(() => {
      this.initializeDefaultData();
    }, 1000);
  }

  private async initializeDefaultData() {
    try {
      // Wait a bit more to ensure database is ready
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Check if we have any products
      const existingProducts = await db.select().from(products).limit(1);

      if (existingProducts.length === 0) {
        console.log("🌱 Initializing default products...");
        // await this.seedProducts();
      } else {
        // Check if we have products with multiple images (imageUrls field)
        const productsWithMultipleImages = await db.select().from(products).where(sql`image_urls IS NOT NULL`).limit(1);
        if (productsWithMultipleImages.length === 0) {
          console.log("🔄 Updating products with multiple images support...");
          // Clear existing products and reseed with multiple images
          await db.delete(products);
          // await this.seedProducts();
        }
      }

      // Check if we have admin user
      const existingAdmin = await db.select().from(users).where(eq(users.username, "admin")).limit(1);

      if (existingAdmin.length === 0) {
        console.log("👤 Creating default admin user...");
        await this.createDefaultAdmin();
      }

      console.log("✅ Database initialization complete");
    } catch (error) {
      console.error("❌ Database initialization failed:", error);
      console.log("⚠️  Retrying in 5 seconds...");
      setTimeout(() => {
        this.initializeDefaultData();
      }, 5000);
    }
  }

  // private async seedProducts() {
  //   const defaultProducts: InsertProduct[] = [
  //     {
  //       name: "Eco-Friendly Dinner Plates",
  //       description: "Biodegradable dinner plates made from sustainable bamboo fiber. Perfect for everyday dining.",
  //       price: "24.99",
  //       category: "plates",
  //       imageUrl: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
  //       imageUrls: [
  //         "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
  //         "https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400",
  //         "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400"
  //       ],
  //       featured: true,
  //       inStock: true,
  //       unitsPerCarton: 50,
  //       unitType: "plates"
  //     },
  //     {
  //       name: "Compostable Food Containers",
  //       description: "Leak-proof containers made from plant-based materials. Ideal for takeout and food storage.",
  //       price: "18.99",
  //       category: "containers",
  //       imageUrl: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400",
  //       imageUrls: [
  //         "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400",
  //         "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400"
  //       ],
  //       featured: true,
  //       inStock: true,

  //     },
  //     {
  //       name: "Bamboo Serving Bowls",
  //       description: "Beautiful handcrafted bowls made from sustainable bamboo. Perfect for salads and soups.",
  //       price: "32.99",
  //       category: "bowls",
  //       imageUrl: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
  //       imageUrls: [
  //         "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
  //         "https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400",
  //         "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400"
  //       ],
  //       featured: true,
  //       inStock: true,

  //     },
  //     {
  //       name: "Biodegradable Cutlery Set",
  //       description: "Complete cutlery set made from cornstarch. Includes forks, knives, and spoons.",
  //       price: "15.99",
  //       category: "cutlery",
  //       imageUrl: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
  //       featured: false,
  //       inStock: true,

  //     },
  //     {
  //       name: "Eco Dining Combo Pack",
  //       description: "Complete eco-friendly dining solution with plates, bowls, and cutlery.",
  //       price: "89.99",
  //       category: "combo",
  //       imageUrl: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
  //       featured: true,
  //       inStock: true,

  //     },
  //     {
  //       name: "Sustainable Lunch Plates",
  //       description: "Compact plates perfect for lunch and light meals. Made from recycled materials.",
  //       price: "19.99",
  //       category: "plates",
  //       imageUrl: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
  //       featured: false,
  //       inStock: true,

  //     },
  //     {
  //       name: "Leak-Proof Storage Containers",
  //       description: "Airtight containers for food storage. Made from biodegradable materials.",
  //       price: "28.99",
  //       category: "containers",
  //       imageUrl: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400",
  //       featured: false,
  //       inStock: true,

  //     },
  //     {
  //       name: "Wooden Salad Bowls",
  //       description: "Elegant wooden bowls perfect for serving salads and side dishes.",
  //       price: "42.99",
  //       category: "bowls",
  //       imageUrl: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
  //       imageUrls: [
  //         "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
  //         "https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400"
  //       ],
  //       featured: false,
  //       inStock: true,

  //     }
  //   ];

  //   await db.insert(products).values(defaultProducts);
  // }

  private async createDefaultAdmin() {
    // Hash the admin password properly
    const hashedPassword = await PasswordUtils.hash("eco-grovea#Admin#");

    const adminUser: UserRegistration = {
      username: "admin",
      email: "<EMAIL>",
      password: hashedPassword,
      firstName: "Admin",
      lastName: "User",
    };

    await db.insert(users).values({
      ...adminUser,
      role: "admin",
      isActive: true,
      isEmailVerified: true,
    });
  }

  // Product methods
  async getProducts(): Promise<Product[]> {
    return await db.select().from(products).orderBy(desc(products.id));
  }

  async getProduct(id: number): Promise<Product | undefined> {
    const result = await db.select().from(products).where(eq(products.id, id)).limit(1);
    return result[0];
  }

  async getProductsByCategory(category: string): Promise<Product[]> {
    return await db.select().from(products).where(eq(products.category, category));
  }

  async getFeaturedProducts(): Promise<Product[]> {
    return await db.select().from(products).where(eq(products.featured, true));
  }

  async searchProducts(query: string): Promise<Product[]> {
    const searchTerm = `%${query}%`;
    return await db.select().from(products).where(
      like(products.name, searchTerm)
    );
  }

  async createProduct(product: InsertProduct): Promise<Product> {
    const result = await db.insert(products).values(product).returning();
    return result[0];
  }

  async updateProduct(id: number, updates: Partial<InsertProduct>): Promise<Product | undefined> {
    const result = await db.update(products)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(products.id, id))
      .returning();
    return result[0];
  }

  async deleteProduct(id: number): Promise<void> {
    await db.delete(products).where(eq(products.id, id));
  }

  // Product variations methods
  async getProductVariations(productId: number): Promise<ProductVariation[]> {
    const result = await db.select()
      .from(productVariations)
      .where(eq(productVariations.productId, productId))
      .orderBy(productVariations.sortOrder, productVariations.id);
    return result;
  }

  async createProductVariation(variation: InsertProductVariation): Promise<ProductVariation> {
    const result = await db.insert(productVariations).values(variation).returning();
    return result[0];
  }

  async deleteProductVariation(variationId: number): Promise<void> {
    await db.delete(productVariations).where(eq(productVariations.id, variationId));
  }

  // Cart methods
  async getCartItems(sessionId: string): Promise<(CartItem & { product: Product })[]> {
    const items = await db.select().from(cartItems).where(eq(cartItems.sessionId, sessionId));

    const itemsWithProducts: (CartItem & { product: Product })[] = [];

    for (const item of items) {
      const product = await this.getProduct(item.productId);
      if (product) {
        let itemWithProduct: any = { ...item, product };

        // If the item has a variation, fetch the variation details
        if (item.variationId) {
          const variation = await db.select()
            .from(productVariations)
            .where(eq(productVariations.id, item.variationId))
            .limit(1);

          if (variation.length > 0) {
            itemWithProduct.variation = variation[0];
          }
        }

        itemsWithProducts.push(itemWithProduct);
      }
    }

    return itemsWithProducts;
  }

  async addToCart(item: InsertCartItem): Promise<CartItem> {
    // Check if item already exists in cart (considering both product and variation)
    const conditions = [
      eq(cartItems.sessionId, item.sessionId),
      eq(cartItems.productId, item.productId)
    ];

    // Add variation condition - treat null and undefined as the same
    if (item.variationId) {
      conditions.push(eq(cartItems.variationId, item.variationId));
    } else {
      conditions.push(isNull(cartItems.variationId));
    }

    const existing = await db.select().from(cartItems)
      .where(and(...conditions))
      .limit(1);

    if (existing.length > 0) {
      // Update quantity for existing item
      const result = await db.update(cartItems)
        .set({ quantity: existing[0].quantity + item.quantity })
        .where(eq(cartItems.id, existing[0].id))
        .returning();
      return result[0];
    } else {
      // Insert new item
      const result = await db.insert(cartItems).values(item).returning();
      return result[0];
    }
  }

  async updateCartItem(sessionId: string, productId: number, quantity: number, variationId?: number): Promise<CartItem | undefined> {
    const conditions = [
      eq(cartItems.sessionId, sessionId),
      eq(cartItems.productId, productId)
    ];

    if (variationId) {
      conditions.push(eq(cartItems.variationId, variationId));
    }

    const result = await db.update(cartItems)
      .set({ quantity })
      .where(and(...conditions))
      .returning();
    return result[0];
  }

  async removeFromCart(sessionId: string, productId: number, variationId?: number): Promise<void> {
    const conditions = [
      eq(cartItems.sessionId, sessionId),
      eq(cartItems.productId, productId)
    ];

    if (variationId) {
      conditions.push(eq(cartItems.variationId, variationId));
    }

    await db.delete(cartItems).where(and(...conditions));
  }

  async clearCart(sessionId: string): Promise<void> {
    await db.delete(cartItems).where(eq(cartItems.sessionId, sessionId));
  }

  // Order methods
  async createOrder(insertOrder: InsertOrder): Promise<Order> {
    // Only include fields that exist in the current database schema
    const orderData = {
      customerName: insertOrder.customerName,
      customerEmail: insertOrder.customerEmail,
      customerPhone: insertOrder.customerPhone,
      shippingAddress: insertOrder.shippingAddress,
      items: insertOrder.items,
      subtotal: insertOrder.subtotal,
      tax: insertOrder.tax,
      total: insertOrder.total,
      status: insertOrder.status || "pending",
      seen: false,
    };

    // Add new fields only if they exist in the schema (for future compatibility)
    try {
      const result = await db.insert(orders).values(orderData).returning();
      return {
        ...result[0],
        // Add default values for new fields that might not be in DB yet
        isGuest: insertOrder.isGuest ?? true,
        userId: insertOrder.userId ?? null,
        sessionId: insertOrder.sessionId ?? null,
        deliveryLocation: insertOrder.deliveryLocation ?? null,
        specialRequests: insertOrder.specialRequests ?? null,
      } as Order;
    } catch (error) {
      console.error("Order creation error:", error);
      throw error;
    }
  }

  async getOrder(id: number): Promise<Order | undefined> {
    const result = await db.select().from(orders).where(eq(orders.id, id)).limit(1);
    return result[0];
  }

  async getOrders(): Promise<Order[]> {
    return await db.select().from(orders).orderBy(desc(orders.id));
  }

  async markOrderAsSeen(id: number, seen: boolean): Promise<Order | undefined> {
    const result = await db.update(orders)
      .set({ seen })
      .where(eq(orders.id, id))
      .returning();
    return result[0];
  }

  async updateOrderStatus(id: number, status: string): Promise<Order | undefined> {
    const result = await db.update(orders)
      .set({ status })
      .where(eq(orders.id, id))
      .returning();
    return result[0];
  }

  async deleteOrder(id: number): Promise<void> {
    await db.delete(orders).where(eq(orders.id, id));
  }

  // Contact methods
  async createContact(insertContact: InsertContact): Promise<Contact> {
    const result = await db.insert(contacts).values({
      ...insertContact,
      seen: false,
    }).returning();
    return result[0];
  }

  async getContacts(): Promise<Contact[]> {
    return await db.select().from(contacts).orderBy(desc(contacts.id));
  }

  async markContactAsSeen(id: number, seen: boolean): Promise<Contact | undefined> {
    const result = await db.update(contacts)
      .set({ seen })
      .where(eq(contacts.id, id))
      .returning();
    return result[0];
  }

  async deleteContact(id: number): Promise<void> {
    await db.delete(contacts).where(eq(contacts.id, id));
  }

  // User management methods
  async createUser(userData: UserRegistration): Promise<User> {
    // Hash the password before storing
    const hashedPassword = await PasswordUtils.hash(userData.password);

    const result = await db.insert(users).values({
      ...userData,
      password: hashedPassword,
      role: "user",
      isActive: true,
      isEmailVerified: false,
    }).returning();
    return result[0];
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.username, username)).limit(1);
    return result[0];
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.email, email)).limit(1);
    return result[0];
  }

  async getUserById(id: number): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
    return result[0];
  }

  async updateUser(id: number, updates: Partial<User>): Promise<User | undefined> {
    const result = await db.update(users)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(users.id, id))
      .returning();
    return result[0];
  }

  async incrementLoginAttempts(userId: number): Promise<void> {
    const user = await this.getUserById(userId);
    if (user) {
      await db.update(users)
        .set({
          loginAttempts: user.loginAttempts + 1,
          updatedAt: new Date()
        })
        .where(eq(users.id, userId));
    }
  }

  async resetLoginAttempts(userId: number): Promise<void> {
    await db.update(users)
      .set({ 
        loginAttempts: 0,
        lockedUntil: null,
        updatedAt: new Date()
      })
      .where(eq(users.id, userId));
  }

  async lockUser(userId: number, lockUntil: Date): Promise<void> {
    await db.update(users)
      .set({ 
        lockedUntil: lockUntil,
        updatedAt: new Date()
      })
      .where(eq(users.id, userId));
  }

  // Session management methods
  async createSession(userId: number, expiresAt: Date): Promise<UserSession> {
    const sessionToken = Math.random().toString(36).substring(2) + Date.now().toString(36);
    const result = await db.insert(userSessions).values({
      userId,
      sessionToken,
      expiresAt,
    }).returning();
    return result[0];
  }

  async getSession(sessionToken: string): Promise<UserSession | undefined> {
    const result = await db.select().from(userSessions)
      .where(and(
        eq(userSessions.sessionToken, sessionToken),
        // Only return non-expired sessions
      ))
      .limit(1);
    
    if (result.length > 0 && result[0].expiresAt > new Date()) {
      return result[0];
    }
    
    // Clean up expired session
    if (result.length > 0) {
      await this.deleteSession(sessionToken);
    }
    
    return undefined;
  }

  async deleteSession(sessionToken: string): Promise<void> {
    await db.delete(userSessions).where(eq(userSessions.sessionToken, sessionToken));
  }

  async cleanupExpiredSessions(): Promise<void> {
    const now = new Date();
    await db.delete(userSessions).where(
      lt(userSessions.expiresAt, now)
    );
  }

  // User authentication and security methods (enhanced versions)

  async updateUserLoginAttempts(userId: number, attempts: number, shouldLock: boolean) {
    const updateData: any = {
      loginAttempts: attempts,
      updatedAt: new Date()
    };

    if (shouldLock) {
      updateData.lockedUntil = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
    } else if (attempts === 0) {
      updateData.lockedUntil = null;
    }

    await db.update(users)
      .set(updateData)
      .where(eq(users.id, userId));
  }

  async updateUserLastLogin(userId: number) {
    await db.update(users)
      .set({
        lastLogin: new Date(),
        updatedAt: new Date()
      })
      .where(eq(users.id, userId));
  }

  async updateUser2FASecret(userId: number, secret: string) {
    await db.update(users)
      .set({
        twoFactorSecret: secret,
        updatedAt: new Date()
      })
      .where(eq(users.id, userId));
  }

  async enable2FA(userId: number, backupCodes: string[]) {
    await db.update(users)
      .set({
        twoFactorEnabled: true,
        twoFactorBackupCodes: backupCodes,
        updatedAt: new Date()
      })
      .where(eq(users.id, userId));
  }

  async disable2FA(userId: number) {
    await db.update(users)
      .set({
        twoFactorEnabled: false,
        twoFactorSecret: null,
        twoFactorBackupCodes: null,
        updatedAt: new Date()
      })
      .where(eq(users.id, userId));
  }

  async updateUserBackupCodes(userId: number, backupCodes: string[]) {
    await db.update(users)
      .set({
        twoFactorBackupCodes: backupCodes,
        updatedAt: new Date()
      })
      .where(eq(users.id, userId));
  }



  async updateUserPassword(userId: number, newPassword: string) {
    const hashedPassword = await PasswordUtils.hash(newPassword);

    await db.update(users)
      .set({
        password: hashedPassword,
        passwordChangedAt: new Date(),
        updatedAt: new Date(),
        // Reset login attempts when password is changed
        loginAttempts: 0,
        lockedUntil: null
      })
      .where(eq(users.id, userId));
  }

  // Security logging methods
  async logSecurityEvent(event: {
    userId?: number;
    eventType: string;
    ipAddress: string;
    userAgent?: string;
    details?: Record<string, any>;
    severity?: string;
  }) {
    await db.insert(securityLogs).values({
      userId: event.userId || null,
      eventType: event.eventType,
      ipAddress: event.ipAddress,
      userAgent: event.userAgent || null,
      details: event.details || null,
      severity: event.severity || 'INFO',
    });
  }

  async getSecurityLogs(limit: number = 100, offset: number = 0) {
    return await db.select().from(securityLogs)
      .orderBy(desc(securityLogs.createdAt))
      .limit(limit)
      .offset(offset);
  }

  async getSecurityLogsByUser(userId: number, limit: number = 50) {
    return await db.select().from(securityLogs)
      .where(eq(securityLogs.userId, userId))
      .orderBy(desc(securityLogs.createdAt))
      .limit(limit);
  }
}

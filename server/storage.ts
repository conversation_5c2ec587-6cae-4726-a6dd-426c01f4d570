import {
  type Product,
  type InsertProduct,
  type ProductVariation,
  type InsertProductVariation,
  type CartItem,
  type InsertCartItem,
  type Order,
  type InsertOrder,
  type Contact,
  type InsertContact,
  type User,
  type InsertUser,
  type UserSession,
  type UserLogin,
  type UserRegistration
} from "@shared/schema";
import { DatabaseStorage } from "./db-storage";

export interface IStorage {
  // Products
  getProducts(): Promise<Product[]>;
  getProduct(id: number): Promise<Product | undefined>;
  getProductsByCategory(category: string): Promise<Product[]>;
  getFeaturedProducts(): Promise<Product[]>;
  searchProducts(query: string): Promise<Product[]>;
  createProduct(product: InsertProduct): Promise<Product>;
  updateProduct(id: number, updates: Partial<InsertProduct>): Promise<Product | undefined>;
  deleteProduct(id: number): Promise<void>;

  // Product Variations
  getProductVariations(productId: number): Promise<ProductVariation[]>;
  createProductVariation(variation: InsertProductVariation): Promise<ProductVariation>;
  deleteProductVariation(variationId: number): Promise<void>;

  // Cart
  getCartItems(sessionId: string): Promise<(CartItem & { product: Product })[]>;
  addToCart(item: InsertCartItem): Promise<CartItem>;
  updateCartItem(sessionId: string, productId: number, quantity: number): Promise<CartItem | undefined>;
  removeFromCart(sessionId: string, productId: number, variationId?: number): Promise<void>;
  clearCart(sessionId: string): Promise<void>;
  
  // Orders
  createOrder(order: InsertOrder): Promise<Order>;
  getOrder(id: number): Promise<Order | undefined>;
  
  // Contacts
  createContact(contact: InsertContact): Promise<Contact>;
  
  // User management
  createUser(user: UserRegistration): Promise<User>;
  getUserByUsername(username: string): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  getUserById(id: number): Promise<User | undefined>;
  updateUser(id: number, updates: Partial<User>): Promise<User | undefined>;
  incrementLoginAttempts(userId: number): Promise<void>;
  resetLoginAttempts(userId: number): Promise<void>;
  lockUser(userId: number, lockUntil: Date): Promise<void>;

  // Session management
  createSession(userId: number, expiresAt: Date): Promise<UserSession>;
  getSession(sessionToken: string): Promise<UserSession | undefined>;
  deleteSession(sessionToken: string): Promise<void>;
  cleanupExpiredSessions(): Promise<void>;

  // Admin methods
  getContacts(): Promise<Contact[]>;
  getOrders(): Promise<Order[]>;
  markOrderAsSeen(id: number, seen: boolean): Promise<Order | undefined>;
  updateOrderStatus(id: number, status: string): Promise<Order | undefined>;
  markContactAsSeen(id: number, seen: boolean): Promise<Contact | undefined>;
  deleteOrder(id: number): Promise<void>;
  deleteContact(id: number): Promise<void>;
}

export class MemStorage implements IStorage {
  private products: Map<number, Product>;
  private cartItems: Map<string, Map<string, CartItem>>;
  private orders: Map<number, Order>;
  private contacts: Map<number, Contact>;
  private users: Map<number, User>;
  private sessions: Map<string, UserSession>;
  private currentProductId: number;
  private currentCartItemId: number;
  private currentOrderId: number;
  private currentContactId: number;
  private currentUserId: number;
  private currentSessionId: number;

  constructor() {
    this.products = new Map();
    this.cartItems = new Map();
    this.orders = new Map();
    this.contacts = new Map();
    this.users = new Map();
    this.sessions = new Map();
    this.currentProductId = 1;
    this.currentCartItemId = 1;
    this.currentOrderId = 1;
    this.currentContactId = 1;
    this.currentUserId = 1;
    this.currentSessionId = 1;

    this.initializeProducts();
    this.initializeDefaultAdmin();
  }

  private initializeProducts() {
    const sampleProducts = [
      {
        name: "Eco-Friendly Dinner Plates",
        description: "Biodegradable dinner plates made from sustainable bamboo fiber.",
        price: "24.99",
        category: "plates",
        imageUrl: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
        inStock: true,
        featured: true
      },
      {
        name: "Compostable Food Containers",
        description: "Eco-friendly takeout containers with secure lids.",
        price: "32.99",
        category: "containers",
        imageUrl: "https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=400",
        inStock: true,
        featured: true
      },
      {
        name: "Bamboo Fiber Bowls",
        description: "Durable bowls perfect for soups and salads.",
        price: "28.99",
        category: "bowls",
        imageUrl: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
        inStock: true,
        featured: false
      },
      {
        name: "Compostable Cutlery Set",
        description: "Plant-based cutlery set including forks, knives, and spoons.",
        price: "18.99",
        category: "cutlery",
        imageUrl: "https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400",
        inStock: true,
        featured: false
      }
    ];

    sampleProducts.forEach(product => {
      const id = this.currentProductId++;
      const fullProduct: Product = {
        ...product,
        id,
        originalPrice: null,
        imageUrls: null,
        inStock: product.inStock ?? true,
        featured: product.featured ?? false,
        rating: "4.5",
        reviewCount: 0,
        specifications: null,
        tags: null,
        hasVariations: false,
        // Default carton information
        unitsPerCarton: 25,
        unitType: "pieces",
        cartonWeight: null,
        cartonDimensions: null,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      this.products.set(id, fullProduct);
    });
  }

  private initializeDefaultAdmin() {
    // Create default admin user
    const adminUser: User = {
      id: this.currentUserId++,
      username: "admin",
      email: "<EMAIL>",
      password: "admin123", // In production, this should be hashed
      firstName: "Admin",
      lastName: "User",
      role: "admin",
      isActive: true,
      isEmailVerified: true,
      lastLogin: null,
      loginAttempts: 0,
      lockedUntil: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.users.set(adminUser.id, adminUser);
  }

  async getProducts(): Promise<Product[]> {
    return Array.from(this.products.values());
  }

  async getProduct(id: number): Promise<Product | undefined> {
    return this.products.get(id);
  }

  async getProductsByCategory(category: string): Promise<Product[]> {
    return Array.from(this.products.values()).filter(p => p.category === category);
  }

  async getFeaturedProducts(): Promise<Product[]> {
    return Array.from(this.products.values()).filter(p => p.featured);
  }

  async searchProducts(query: string): Promise<Product[]> {
    const lowercaseQuery = query.toLowerCase();
    return Array.from(this.products.values()).filter(p =>
      p.name.toLowerCase().includes(lowercaseQuery) ||
      p.description.toLowerCase().includes(lowercaseQuery) ||
      p.category.toLowerCase().includes(lowercaseQuery) ||
      (p.tags && p.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery)))
    );
  }

  async createProduct(insertProduct: InsertProduct): Promise<Product> {
    const id = this.currentProductId++;
    const product: Product = {
      ...insertProduct,
      id,
      originalPrice: insertProduct.originalPrice || null,
      imageUrls: insertProduct.imageUrls ?? null,
      inStock: insertProduct.inStock ?? true,
      featured: insertProduct.featured ?? false,
      rating: insertProduct.rating ?? "4.5",
      reviewCount: insertProduct.reviewCount ?? 0,
      specifications: insertProduct.specifications ?? null,
      tags: insertProduct.tags ?? null,
      hasVariations: insertProduct.hasVariations ?? false,
      // Carton information with proper null handling
      unitsPerCarton: insertProduct.unitsPerCarton || 1,
      unitType: insertProduct.unitType || "pieces",
      cartonWeight: insertProduct.cartonWeight || null,
      cartonDimensions: insertProduct.cartonDimensions || null,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    this.products.set(id, product);
    return product;
  }

  async updateProduct(id: number, updates: Partial<InsertProduct>): Promise<Product | undefined> {
    const product = this.products.get(id);
    if (product) {
      const updatedProduct = { ...product, ...updates };
      this.products.set(id, updatedProduct);
      return updatedProduct;
    }
    return undefined;
  }

  async deleteProduct(id: number): Promise<void> {
    this.products.delete(id);
  }

  async getProductVariations(productId: number): Promise<ProductVariation[]> {
    // For MemStorage, return empty array since we don't store variations in memory
    // In a real implementation, you'd store variations in a Map similar to products
    return [];
  }

  async createProductVariation(variation: InsertProductVariation): Promise<ProductVariation> {
    // For MemStorage, just return a mock variation
    // In a real implementation, you'd store this in a Map
    const mockVariation: ProductVariation = {
      id: Date.now(), // Simple ID generation
      productId: variation.productId,
      name: variation.name,
      type: variation.type,
      value: variation.value,
      price: variation.price || null,
      originalPrice: variation.originalPrice || null,
      stockQuantity: variation.stockQuantity || 0,
      sku: variation.sku || null,
      isActive: variation.isActive !== false,
      sortOrder: variation.sortOrder || 0,
      // Carton information (optional for variations)
      unitsPerCarton: variation.unitsPerCarton || null,
      unitType: variation.unitType || null,
      cartonWeight: variation.cartonWeight || null,
      cartonDimensions: variation.cartonDimensions || null,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    return mockVariation;
  }

  async deleteProductVariation(variationId: number): Promise<void> {
    // For MemStorage, this is a no-op since we don't store variations
    // In a real implementation, you'd remove from the variations Map
    console.log('MemStorage: Deleting variation', variationId);
  }

  async getCartItems(sessionId: string): Promise<(CartItem & { product: Product })[]> {
    const sessionCart = this.cartItems.get(sessionId) || new Map();
    const items: (CartItem & { product: Product })[] = [];
    
    sessionCart.forEach((cartItem) => {
      const product = this.products.get(cartItem.productId);
      if (product) {
        items.push({ ...cartItem, product });
      }
    });
    
    return items;
  }

  async addToCart(insertItem: InsertCartItem): Promise<CartItem> {
    const { sessionId, productId, quantity, variationId } = insertItem;

    if (!this.cartItems.has(sessionId)) {
      this.cartItems.set(sessionId, new Map());
    }

    const sessionCart = this.cartItems.get(sessionId)!;

    // Create a unique key that includes both productId and variationId
    const cartKey = `${productId}-${variationId || 'no-variation'}`;
    const existingItem = sessionCart.get(cartKey);

    if (existingItem) {
      existingItem.quantity += quantity;
      return existingItem;
    } else {
      const id = this.currentCartItemId++;
      const cartItem: CartItem = {
        ...insertItem,
        id,
        variationId: insertItem.variationId ?? null,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      sessionCart.set(cartKey, cartItem);
      return cartItem;
    }
  }

  async updateCartItem(sessionId: string, productId: number, quantity: number, variationId?: number): Promise<CartItem | undefined> {
    const sessionCart = this.cartItems.get(sessionId);
    if (!sessionCart) return undefined;

    const cartKey = `${productId}-${variationId || 'no-variation'}`;
    const item = sessionCart.get(cartKey);
    if (!item) return undefined;

    if (quantity <= 0) {
      sessionCart.delete(cartKey);
      return undefined;
    }

    item.quantity = quantity;
    return item;
  }

  async removeFromCart(sessionId: string, productId: number, variationId?: number): Promise<void> {
    const sessionCart = this.cartItems.get(sessionId);
    if (sessionCart) {
      const cartKey = `${productId}-${variationId || 'no-variation'}`;
      sessionCart.delete(cartKey);
    }
  }

  async clearCart(sessionId: string): Promise<void> {
    this.cartItems.delete(sessionId);
  }

  async createOrder(insertOrder: InsertOrder): Promise<Order> {
    const id = this.currentOrderId++;
    const order: Order = {
      ...insertOrder,
      id,
      status: insertOrder.status || "pending",
      customerPhone: insertOrder.customerPhone || null,
      seen: false,
      isGuest: insertOrder.isGuest ?? true,
      userId: insertOrder.userId ?? null,
      sessionId: insertOrder.sessionId ?? null,
      deliveryLocation: insertOrder.deliveryLocation ?? null,
      specialRequests: insertOrder.specialRequests ?? null,
      createdAt: new Date()
    };
    this.orders.set(id, order);
    return order;
  }

  async getOrder(id: number): Promise<Order | undefined> {
    return this.orders.get(id);
  }

  async createContact(insertContact: InsertContact): Promise<Contact> {
    const id = this.currentContactId++;
    const contact: Contact = {
      ...insertContact,
      id,
      company: insertContact.company || null,
      phone: insertContact.phone || null,
      seen: false,
      createdAt: new Date()
    };
    this.contacts.set(id, contact);
    return contact;
  }

  // User management methods
  async createUser(userData: UserRegistration): Promise<User> {
    const id = this.currentUserId++;
    const user: User = {
      id,
      username: userData.username,
      email: userData.email,
      password: userData.password, // In production, hash this
      firstName: userData.firstName,
      lastName: userData.lastName,
      role: "user",
      isActive: true,
      isEmailVerified: false,
      lastLogin: null,
      loginAttempts: 0,
      lockedUntil: null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.users.set(id, user);
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(u => u.username === username);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(u => u.email === email);
  }

  async getUserById(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async updateUser(id: number, updates: Partial<User>): Promise<User | undefined> {
    const user = this.users.get(id);
    if (user) {
      const updatedUser = { ...user, ...updates, updatedAt: new Date() };
      this.users.set(id, updatedUser);
      return updatedUser;
    }
    return undefined;
  }

  async incrementLoginAttempts(userId: number): Promise<void> {
    const user = this.users.get(userId);
    if (user) {
      user.loginAttempts += 1;
      user.updatedAt = new Date();
      this.users.set(userId, user);
    }
  }

  async resetLoginAttempts(userId: number): Promise<void> {
    const user = this.users.get(userId);
    if (user) {
      user.loginAttempts = 0;
      user.lockedUntil = null;
      user.updatedAt = new Date();
      this.users.set(userId, user);
    }
  }

  async lockUser(userId: number, lockUntil: Date): Promise<void> {
    const user = this.users.get(userId);
    if (user) {
      user.lockedUntil = lockUntil;
      user.updatedAt = new Date();
      this.users.set(userId, user);
    }
  }

  // Session management methods
  async createSession(userId: number, expiresAt: Date): Promise<UserSession> {
    const sessionToken = Math.random().toString(36).substring(2) + Date.now().toString(36);
    const session: UserSession = {
      id: this.currentSessionId++,
      userId,
      sessionToken,
      expiresAt,
      createdAt: new Date(),
    };
    this.sessions.set(sessionToken, session);
    return session;
  }

  async getSession(sessionToken: string): Promise<UserSession | undefined> {
    const session = this.sessions.get(sessionToken);
    if (session && session.expiresAt > new Date()) {
      return session;
    }
    if (session) {
      this.sessions.delete(sessionToken);
    }
    return undefined;
  }

  async deleteSession(sessionToken: string): Promise<void> {
    this.sessions.delete(sessionToken);
  }

  async cleanupExpiredSessions(): Promise<void> {
    const now = new Date();
    const expiredTokens: string[] = [];

    this.sessions.forEach((session, token) => {
      if (session.expiresAt <= now) {
        expiredTokens.push(token);
      }
    });

    expiredTokens.forEach(token => {
      this.sessions.delete(token);
    });
  }

  async getContacts(): Promise<Contact[]> {
    return Array.from(this.contacts.values());
  }

  async getOrders(): Promise<Order[]> {
    return Array.from(this.orders.values());
  }

  async markOrderAsSeen(id: number, seen: boolean): Promise<Order | undefined> {
    const order = this.orders.get(id);
    if (order) {
      order.seen = seen;
      this.orders.set(id, order);
      return order;
    }
    return undefined;
  }

  async updateOrderStatus(id: number, status: string): Promise<Order | undefined> {
    const order = this.orders.get(id);
    if (order) {
      order.status = status;
      this.orders.set(id, order);
      return order;
    }
    return undefined;
  }

  async markContactAsSeen(id: number, seen: boolean): Promise<Contact | undefined> {
    const contact = this.contacts.get(id);
    if (contact) {
      contact.seen = seen;
      this.contacts.set(id, contact);
      return contact;
    }
    return undefined;
  }

  async deleteContact(id: number): Promise<void> {
    this.contacts.delete(id);
  }

  async deleteOrder(id: number): Promise<void> {
    this.orders.delete(id);
  }
}

export const storage = new DatabaseStorage();

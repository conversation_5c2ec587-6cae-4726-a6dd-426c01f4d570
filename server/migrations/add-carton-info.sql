-- Add carton information fields to products table
ALTER TABLE products
ADD COLUMN units_per_carton INTEGER NOT NULL DEFAULT 1,
ADD COLUMN unit_type TEXT NOT NULL DEFAULT 'pieces',
ADD COLUMN carton_weight DECIMAL(8,2),
ADD COLUMN carton_dimensions TEXT;

-- Add carton information fields to product variations table
ALTER TABLE product_variations
ADD COLUMN units_per_carton INTEGER,
ADD COLUMN unit_type TEXT,
ADD COLUMN carton_weight DECIMAL(8,2),
ADD COLUMN carton_dimensions TEXT;

-- Update existing products with sample carton information
UPDATE products SET 
  units_per_carton = CASE 
    WHEN category = 'plates' THEN 50
    WHEN category = 'bowls' THEN 25
    WHEN category = 'containers' THEN 100
    WHEN category = 'cutlery' THEN 200
    WHEN category = 'combo' THEN 10
    ELSE 25
  END,
  unit_type = CASE 
    WHEN category = 'plates' THEN 'plates'
    WHEN category = 'bowls' THEN 'bowls'
    WHEN category = 'containers' THEN 'containers'
    WHEN category = 'cutlery' THEN 'pieces'
    WHEN category = 'combo' THEN 'sets'
    ELSE 'pieces'
  END,
  carton_weight = CASE 
    WHEN category = 'plates' THEN 2.5
    WHEN category = 'bowls' THEN 1.8
    WHEN category = 'containers' THEN 3.2
    WHEN category = 'cutlery' THEN 1.5
    WHEN category = 'combo' THEN 4.0
    ELSE 2.0
  END,
  carton_dimensions = CASE 
    WHEN category = 'plates' THEN '40cm x 30cm x 20cm'
    WHEN category = 'bowls' THEN '35cm x 25cm x 18cm'
    WHEN category = 'containers' THEN '45cm x 35cm x 25cm'
    WHEN category = 'cutlery' THEN '30cm x 20cm x 15cm'
    WHEN category = 'combo' THEN '50cm x 40cm x 30cm'
    ELSE '35cm x 25cm x 20cm'
  END;

import { sql } from "./db";

/**
 * Migration to add imageUrls column to products table
 * This supports storing multiple images (up to 3) for each product
 */
export async function migrateImageUrls() {
  try {
    console.log("🔄 Adding imageUrls column to products table...");
    
    // Add the imageUrls column as <PERSON><PERSON><PERSON>
    await sql`
      ALTER TABLE products 
      ADD COLUMN IF NOT EXISTS image_urls JSON
    `;
    
    console.log("✅ Successfully added imageUrls column");
    
    // Update existing products to have imageUrls array with their current imageUrl
    console.log("🔄 Migrating existing product images...");
    
    await sql`
      UPDATE products 
      SET image_urls = JSON_ARRAY(image_url) 
      WHERE image_urls IS NULL AND image_url IS NOT NULL
    `;
    
    console.log("✅ Successfully migrated existing product images");
    console.log("📝 Products now support up to 3 images each");
    
    return true;
  } catch (error) {
    console.error("❌ Migration failed:", error);
    return false;
  }
}

// Run migration immediately
migrateImageUrls()
  .then((success) => {
    if (success) {
      console.log("🎉 Migration completed successfully!");
      process.exit(0);
    } else {
      console.log("💥 Migration failed!");
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error("💥 Migration error:", error);
    process.exit(1);
  });

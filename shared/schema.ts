import { pgTable, text, serial, integer, decimal, boolean, json, timestamp } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const products = pgTable("products", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  description: text("description").notNull(),
  price: decimal("price", { precision: 10, scale: 2 }).notNull(), // Price per carton
  originalPrice: decimal("original_price", { precision: 10, scale: 2 }),
  category: text("category").notNull(),
  imageUrl: text("image_url").notNull(),
  imageUrls: json("image_urls").$type<string[]>(), // Support for multiple images (max 3)
  inStock: boolean("in_stock").notNull().default(true),
  featured: boolean("featured").notNull().default(false),
  rating: decimal("rating", { precision: 2, scale: 1 }).notNull().default("4.5"),
  reviewCount: integer("review_count").notNull().default(0),
  specifications: json("specifications").$type<Record<string, string>>(),
  tags: json("tags").$type<string[]>(),
  hasVariations: boolean("has_variations").notNull().default(false),
  // Carton/Packaging information
  unitsPerCarton: integer("units_per_carton").notNull().default(1), // How many units in one carton
  unitType: text("unit_type").notNull().default("pieces"), // e.g., "pieces", "items", "plates", "bowls"
  cartonWeight: decimal("carton_weight", { precision: 8, scale: 2 }), // Weight in kg
  cartonDimensions: text("carton_dimensions"), // e.g., "30cm x 20cm x 15cm"
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Product variations table for sizes, models, etc.
export const productVariations = pgTable("product_variations", {
  id: serial("id").primaryKey(),
  productId: integer("product_id").notNull().references(() => products.id, { onDelete: "cascade" }),
  name: text("name").notNull(), // e.g., "Small", "Large", "Model A", "Premium"
  type: text("type").notNull(), // e.g., "size", "model", "color"
  value: text("value").notNull(), // e.g., "S", "L", "A", "Premium"
  price: decimal("price", { precision: 10, scale: 2 }), // Optional price override
  originalPrice: decimal("original_price", { precision: 10, scale: 2 }),
  stockQuantity: integer("stock_quantity").notNull().default(0),
  sku: text("sku"), // Stock Keeping Unit
  isActive: boolean("is_active").notNull().default(true),
  sortOrder: integer("sort_order").notNull().default(0),
  // Variation-specific carton/packaging information (optional, falls back to product defaults)
  unitsPerCarton: integer("units_per_carton"), // How many units in one carton for this variation
  unitType: text("unit_type"), // e.g., "pieces", "items", "plates", "bowls" for this variation
  cartonWeight: decimal("carton_weight", { precision: 8, scale: 2 }), // Weight in kg for this variation
  cartonDimensions: text("carton_dimensions"), // e.g., "30cm x 20cm x 15cm" for this variation
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const cartItems = pgTable("cart_items", {
  id: serial("id").primaryKey(),
  sessionId: text("session_id").notNull(),
  productId: integer("product_id").notNull(),
  variationId: integer("variation_id").references(() => productVariations.id, { onDelete: "cascade" }),
  quantity: integer("quantity").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const orders = pgTable("orders", {
  id: serial("id").primaryKey(),
  customerName: text("customer_name").notNull(),
  customerEmail: text("customer_email").notNull(),
  customerPhone: text("customer_phone"),
  shippingAddress: json("shipping_address").$type<{
    street: string;
    city: string;
    state: string;
    zipCode: string;
    country: string;
  }>().notNull(),
  items: json("items").$type<Array<{
    productId: number;
    productName: string;
    quantity: number;
    price: string;
    // Variation details
    variationId?: number;
    variationName?: string;
    variationType?: string;
    variationValue?: string;
    // Carton information
    unitsPerCarton?: number;
    unitType?: string;
    pricePerUnit?: string;
  }>>().notNull(),
  subtotal: decimal("subtotal", { precision: 10, scale: 2 }).notNull(),
  tax: decimal("tax", { precision: 10, scale: 2 }).notNull(),
  total: decimal("total", { precision: 10, scale: 2 }).notNull(),
  status: text("status").notNull().default("pending"),
  seen: boolean("seen").notNull().default(false),
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

export const contacts = pgTable("contacts", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull(),
  company: text("company"),
  phone: text("phone"),
  inquiryType: text("inquiry_type").notNull(),
  message: text("message").notNull(),
  seen: boolean("seen").notNull().default(false),
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  firstName: text("first_name").notNull(),
  lastName: text("last_name").notNull(),
  role: text("role").notNull().default("user"), // 'admin', 'user', 'guest'
  isActive: boolean("is_active").notNull().default(true),
  isEmailVerified: boolean("is_email_verified").notNull().default(false),
  lastLogin: timestamp("last_login"),
  loginAttempts: integer("login_attempts").notNull().default(0),
  lockedUntil: timestamp("locked_until"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const userSessions = pgTable("user_sessions", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull(),
  sessionToken: text("session_token").notNull().unique(),
  expiresAt: timestamp("expires_at").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
});

export const insertProductSchema = createInsertSchema(products).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  imageUrls: z.array(z.string().url()).max(3, "Maximum 3 images allowed").optional(),
  unitsPerCarton: z.number().min(1, "Units per carton must be at least 1").default(1),
  unitType: z.string().min(1, "Unit type is required").default("pieces"),
  cartonWeight: z.string().optional(),
  cartonDimensions: z.string().optional(),
});

export const insertProductVariationSchema = createInsertSchema(productVariations).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  unitsPerCarton: z.number().min(1, "Units per carton must be at least 1").optional(),
  unitType: z.string().min(1, "Unit type is required").optional(),
  cartonWeight: z.string().optional(),
  cartonDimensions: z.string().optional(),
});

export const insertCartItemSchema = createInsertSchema(cartItems).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertOrderSchema = createInsertSchema(orders).omit({
  id: true,
  createdAt: true,
}).extend({
  // Override decimal fields to accept strings (as they come from frontend)
  subtotal: z.string(),
  tax: z.string(),
  total: z.string(),
  // Accept additional fields for processing but don't include in DB insert
  deliveryLocation: z.string().optional(),
  specialRequests: z.string().optional(),
  userId: z.number().optional().nullable(),
  isGuest: z.boolean().optional().default(true),
  sessionId: z.string().optional().nullable(),
});

export const insertContactSchema = createInsertSchema(contacts).omit({
  id: true,
  createdAt: true,
});

export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  lastLogin: true,
  loginAttempts: true,
  lockedUntil: true,
});

export const userLoginSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
});

export const userRegistrationSchema = z.object({
  username: z.string().min(3, "Username must be at least 3 characters").max(50),
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  firstName: z.string().min(1, "First name is required").max(50),
  lastName: z.string().min(1, "Last name is required").max(50),
});

export type Product = typeof products.$inferSelect;
export type InsertProduct = z.infer<typeof insertProductSchema>;
export type ProductVariation = typeof productVariations.$inferSelect;
export type InsertProductVariation = z.infer<typeof insertProductVariationSchema>;
export type CartItem = typeof cartItems.$inferSelect;
export type InsertCartItem = z.infer<typeof insertCartItemSchema>;
export type Order = typeof orders.$inferSelect & {
  // Additional fields that may not be in database yet
  userId?: number | null;
  isGuest?: boolean;
  sessionId?: string | null;
  deliveryLocation?: string | null;
  specialRequests?: string | null;
};
export type InsertOrder = z.infer<typeof insertOrderSchema>;
export type Contact = typeof contacts.$inferSelect;
export type InsertContact = z.infer<typeof insertContactSchema>;
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type UserSession = typeof userSessions.$inferSelect;
export type UserLogin = z.infer<typeof userLoginSchema>;
export type UserRegistration = z.infer<typeof userRegistrationSchema>;
